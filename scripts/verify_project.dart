import 'dart:io';

/// 项目完整性验证脚本
/// 验证女性健康功能模块的完整性和正确性
void main() async {
  print('🔍 开始验证女性健康功能模块...\n');

  final results = <String, bool>{};

  // 1. 验证目录结构
  results['目录结构'] = await _verifyDirectoryStructure();

  // 2. 验证核心文件
  results['核心文件'] = await _verifyCoreFiles();

  // 3. 验证模型文件
  results['数据模型'] = await _verifyModelFiles();

  // 4. 验证服务文件
  results['业务服务'] = await _verifyServiceFiles();

  // 5. 验证控制器文件
  results['控制器'] = await _verifyControllerFiles();

  // 6. 验证视图文件
  results['UI视图'] = await _verifyViewFiles();

  // 7. 验证组件文件
  results['UI组件'] = await _verifyWidgetFiles();

  // 8. 验证路由配置
  results['路由配置'] = await _verifyRouteFiles();

  // 9. 验证测试文件
  results['测试文件'] = await _verifyTestFiles();

  // 10. 验证依赖配置
  results['依赖配置'] = await _verifyDependencies();

  // 输出验证结果
  _printResults(results);

  // 生成验证报告
  await _generateReport(results);

  print('\n✅ 验证完成！');
}

/// 验证目录结构
Future<bool> _verifyDirectoryStructure() async {
  print('📁 验证目录结构...');
  
  final requiredDirs = [
    'lib/woman_health',
    'lib/woman_health/controllers',
    'lib/woman_health/models',
    'lib/woman_health/services',
    'lib/woman_health/views',
    'lib/woman_health/widgets',
    'lib/woman_health/routes',
    'lib/woman_health/utils',
    'lib/woman_health/database',
    'test/woman_health',
  ];

  for (final dir in requiredDirs) {
    if (!await Directory(dir).exists()) {
      print('❌ 缺少目录: $dir');
      return false;
    }
  }

  print('✅ 目录结构完整');
  return true;
}

/// 验证核心文件
Future<bool> _verifyCoreFiles() async {
  print('📄 验证核心文件...');
  
  final coreFiles = [
    'lib/woman_health/woman_health.dart',
    'lib/woman_health/app.dart',
    'lib/woman_health_demo.dart',
  ];

  for (final file in coreFiles) {
    if (!await File(file).exists()) {
      print('❌ 缺少核心文件: $file');
      return false;
    }
  }

  print('✅ 核心文件完整');
  return true;
}

/// 验证模型文件
Future<bool> _verifyModelFiles() async {
  print('🏗️ 验证数据模型...');
  
  final modelFiles = [
    'lib/woman_health/models/models.dart',
    'lib/woman_health/models/menstrual_record.dart',
    'lib/woman_health/models/calendar_data.dart',
    'lib/woman_health/models/option_data.dart',
    'lib/woman_health/models/hemorrhage_data.dart',
    'lib/woman_health/models/record_history_data.dart',
    'lib/woman_health/models/card_data.dart',
  ];

  for (final file in modelFiles) {
    if (!await File(file).exists()) {
      print('❌ 缺少模型文件: $file');
      return false;
    }
  }

  print('✅ 数据模型完整');
  return true;
}

/// 验证服务文件
Future<bool> _verifyServiceFiles() async {
  print('⚙️ 验证业务服务...');
  
  final serviceFiles = [
    'lib/woman_health/services/services.dart',
    'lib/woman_health/services/woman_health_service.dart',
    'lib/woman_health/services/forecast_calculation.dart',
    'lib/woman_health/services/ovulation_calculator.dart',
    'lib/woman_health/services/symptom_analyzer.dart',
    'lib/woman_health/services/notification_calculator.dart',
  ];

  for (final file in serviceFiles) {
    if (!await File(file).exists()) {
      print('❌ 缺少服务文件: $file');
      return false;
    }
  }

  print('✅ 业务服务完整');
  return true;
}

/// 验证控制器文件
Future<bool> _verifyControllerFiles() async {
  print('🎮 验证控制器...');
  
  final controllerFiles = [
    'lib/woman_health/controllers/controllers.dart',
    'lib/woman_health/controllers/health_main_controller.dart',
    'lib/woman_health/controllers/guide_controller.dart',
  ];

  for (final file in controllerFiles) {
    if (!await File(file).exists()) {
      print('❌ 缺少控制器文件: $file');
      return false;
    }
  }

  print('✅ 控制器完整');
  return true;
}

/// 验证视图文件
Future<bool> _verifyViewFiles() async {
  print('🖼️ 验证UI视图...');
  
  final viewFiles = [
    'lib/woman_health/views/health_main_view.dart',
    'lib/woman_health/views/guide_view.dart',
    'lib/woman_health/views/add_record_view.dart',
    'lib/woman_health/views/forecast_view.dart',
    'lib/woman_health/views/statistics_view.dart',
    'lib/woman_health/views/settings_view.dart',
    'lib/woman_health/views/calendar_view.dart',
  ];

  for (final file in viewFiles) {
    if (!await File(file).exists()) {
      print('❌ 缺少视图文件: $file');
      return false;
    }
  }

  print('✅ UI视图完整');
  return true;
}

/// 验证组件文件
Future<bool> _verifyWidgetFiles() async {
  print('🧩 验证UI组件...');
  
  final widgetFiles = [
    'lib/woman_health/widgets/widgets.dart',
    'lib/woman_health/widgets/common_widgets.dart',
    'lib/woman_health/widgets/custom_calendar.dart',
  ];

  for (final file in widgetFiles) {
    if (!await File(file).exists()) {
      print('❌ 缺少组件文件: $file');
      return false;
    }
  }

  print('✅ UI组件完整');
  return true;
}

/// 验证路由文件
Future<bool> _verifyRouteFiles() async {
  print('🛣️ 验证路由配置...');
  
  final routeFiles = [
    'lib/woman_health/routes/app_routes.dart',
    'lib/woman_health/routes/app_pages.dart',
  ];

  for (final file in routeFiles) {
    if (!await File(file).exists()) {
      print('❌ 缺少路由文件: $file');
      return false;
    }
  }

  print('✅ 路由配置完整');
  return true;
}

/// 验证测试文件
Future<bool> _verifyTestFiles() async {
  print('🧪 验证测试文件...');
  
  final testFiles = [
    'test/woman_health/models/menstrual_record_test.dart',
    'test/woman_health/services/ovulation_calculator_test.dart',
    'test/woman_health/utils/data_validator_test.dart',
    'test/woman_health/integration/ui_integration_test.dart',
  ];

  for (final file in testFiles) {
    if (!await File(file).exists()) {
      print('❌ 缺少测试文件: $file');
      return false;
    }
  }

  print('✅ 测试文件完整');
  return true;
}

/// 验证依赖配置
Future<bool> _verifyDependencies() async {
  print('📦 验证依赖配置...');
  
  final pubspecFile = File('pubspec.yaml');
  if (!await pubspecFile.exists()) {
    print('❌ 缺少 pubspec.yaml 文件');
    return false;
  }

  final content = await pubspecFile.readAsString();
  final requiredDeps = [
    'get:',
    'sqflite:',
    'json_annotation:',
    'flutter_screenutil:',
  ];

  for (final dep in requiredDeps) {
    if (!content.contains(dep)) {
      print('❌ 缺少依赖: $dep');
      return false;
    }
  }

  print('✅ 依赖配置完整');
  return true;
}

/// 输出验证结果
void _printResults(Map<String, bool> results) {
  print('\n📊 验证结果汇总:');
  print('=' * 50);
  
  int passCount = 0;
  for (final entry in results.entries) {
    final status = entry.value ? '✅ 通过' : '❌ 失败';
    print('${entry.key.padRight(15)} $status');
    if (entry.value) passCount++;
  }
  
  print('=' * 50);
  print('总计: $passCount/${results.length} 项通过');
  
  if (passCount == results.length) {
    print('🎉 所有验证项目都通过了！');
  } else {
    print('⚠️ 有 ${results.length - passCount} 项验证失败，请检查相关文件。');
  }
}

/// 生成验证报告
Future<void> _generateReport(Map<String, bool> results) async {
  final reportFile = File('verification_report.md');
  final buffer = StringBuffer();
  
  buffer.writeln('# 女性健康功能模块验证报告');
  buffer.writeln();
  buffer.writeln('生成时间: ${DateTime.now()}');
  buffer.writeln();
  buffer.writeln('## 验证结果');
  buffer.writeln();
  
  for (final entry in results.entries) {
    final status = entry.value ? '✅ 通过' : '❌ 失败';
    buffer.writeln('- **${entry.key}**: $status');
  }
  
  buffer.writeln();
  buffer.writeln('## 项目结构');
  buffer.writeln();
  buffer.writeln('```');
  buffer.writeln('lib/woman_health/');
  buffer.writeln('├── app.dart                 # 应用入口');
  buffer.writeln('├── controllers/             # GetX控制器');
  buffer.writeln('├── models/                  # 数据模型');
  buffer.writeln('├── services/                # 业务服务');
  buffer.writeln('├── views/                   # UI视图');
  buffer.writeln('├── widgets/                 # UI组件');
  buffer.writeln('├── routes/                  # 路由配置');
  buffer.writeln('├── utils/                   # 工具类');
  buffer.writeln('├── database/                # 数据库');
  buffer.writeln('└── woman_health.dart        # 主导出文件');
  buffer.writeln('```');
  
  await reportFile.writeAsString(buffer.toString());
  print('\n📄 验证报告已生成: verification_report.md');
}
