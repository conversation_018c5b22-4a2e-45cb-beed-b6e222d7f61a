{"app": {"signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"certpath": "/Users/<USER>/.ohos/config/default_ohos_eZKFnkp0GzyCxWpuZv1WWTjrbm0wFmd9foZP0bYf84Q=.cer", "keyAlias": "debugKey", "keyPassword": "0000001A3CF08C0603418337403AAB6CD75A30B508FEC960E853EC369E1DB9656E2155FBD653557A6DF9", "profile": "/Users/<USER>/.ohos/config/default_ohos_eZKFnkp0GzyCxWpuZv1WWTjrbm0wFmd9foZP0bYf84Q=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "/Users/<USER>/.ohos/config/default_ohos_eZKFnkp0GzyCxWpuZv1WWTjrbm0wFmd9foZP0bYf84Q=.p12", "storePassword": "0000001ADB941BC16003D572CC37CA2C66E9D0946101AE2CF338AD132A9B50AAFBF1F4346346F0003067"}}], "products": [{"name": "default", "signingConfig": "default", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS"}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}]}