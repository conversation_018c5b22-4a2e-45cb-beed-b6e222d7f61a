import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/woman_health_service.dart';
import '../services/forecast_calculation.dart';
import '../services/ovulation_calculator.dart';
import '../services/symptom_analyzer.dart';
import '../services/notification_calculator.dart';

/// 女性健康主页面控制器
class HealthMainController extends GetxController implements ForecastCalculationListener {
  static const String _tag = 'HealthMainController';

  // 服务实例
  final WomanHealthService _womanHealthService = WomanHealthService();
  late final ForecastCalculation _forecastCalculation;

  // 响应式状态变量
  final RxBool isLoading = false.obs;
  final RxString currentUserId = ''.obs;
  final RxInt menstrualLength = 5.obs;
  final RxInt menstrualCycleLength = 28.obs;
  final RxBool isMenstrualNotification = true.obs;
  final RxBool isPregnancyNotification = false.obs;

  // 日历相关
  final Rx<DateTime> selectedDate = DateTime.now().obs;
  final RxList<CalendarSelectData> calendarData = <CalendarSelectData>[].obs;
  final RxList<CalendarSelectData> currentWeekData = <CalendarSelectData>[].obs;

  // 预测数据
  final RxList<RecordHistoryData> historyDataList = <RecordHistoryData>[].obs;
  final RxList<RecordHistoryData> menstrualForecastList = <RecordHistoryData>[].obs;
  final RxList<RecordHistoryData> ovulationForecastList = <RecordHistoryData>[].obs;

  // 卡片数据
  final RxList<CardData> cardDataList = <CardData>[].obs;

  // 月经记录
  final RxList<MenstrualRecord> menstrualRecords = <MenstrualRecord>[].obs;
  final Rx<MenstrualRecord?> todayRecord = Rx<MenstrualRecord?>(null);

  // 统计数据
  final RxString nextMenstruationDate = ''.obs;
  final RxString nextOvulationDate = ''.obs;
  final RxInt daysUntilNextMenstruation = 0.obs;
  final RxInt currentCycleDay = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeController();
  }

  @override
  void onReady() {
    super.onReady();
    _loadInitialData();
  }

  @override
  void onClose() {
    _forecastCalculation.dispose();
    super.onClose();
  }

  /// 初始化控制器
  void _initializeController() {
    _forecastCalculation = ForecastCalculation();
    _forecastCalculation.setOnResultListener(this);
    
    // 设置默认用户ID（实际应用中应从用户管理系统获取）
    currentUserId.value = 'current_user';
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    try {
      isLoading.value = true;
      
      // 加载用户设置
      await _loadUserSettings();
      
      // 加载月经记录
      await _loadMenstrualRecords();
      
      // 开始预测计算
      await _startForecastCalculation();
      
      // 生成日历数据
      _generateCalendarData();
      
      // 生成卡片数据
      _generateCardData();
      
    } catch (e) {
      print('$_tag: 加载初始数据失败: $e');
      Get.snackbar('错误', '数据加载失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }

  /// 加载用户设置
  Future<void> _loadUserSettings() async {
    // 这里应该从SharedPreferences或数据库加载用户设置
    // 暂时使用默认值
    menstrualLength.value = 5;
    menstrualCycleLength.value = 28;
    isMenstrualNotification.value = true;
    isPregnancyNotification.value = false;
  }

  /// 加载月经记录
  Future<void> _loadMenstrualRecords() async {
    final records = await _womanHealthService.getMenstrualRecords(currentUserId.value);
    menstrualRecords.value = records;
    
    // 获取今天的记录
    final today = DateTime.now();
    todayRecord.value = await _womanHealthService.getMenstrualRecordByDate(
      currentUserId.value,
      today,
    );
  }

  /// 开始预测计算
  Future<void> _startForecastCalculation() async {
    await _forecastCalculation.onStartCalculation(
      menstrualLength.value,
      menstrualCycleLength.value,
    );
  }

  /// 生成日历数据
  void _generateCalendarData() {
    final List<CalendarSelectData> data = [];
    final now = DateTime.now();
    
    // 生成当前月份前后各一个月的数据
    final startDate = DateTime(now.year, now.month - 1, 1);
    final endDate = DateTime(now.year, now.month + 2, 0);
    
    for (var date = startDate; date.isBefore(endDate.add(const Duration(days: 1))); date = date.add(const Duration(days: 1))) {
      final selectData = CalendarSelectData(
        year: date.year,
        month: date.month,
        day: date.day,
        isCurrentDay: _isSameDay(date, now),
      );
      
      // 查找对应的月经记录
      final record = menstrualRecords.firstWhereOrNull(
        (r) => r.year == date.year && r.month == date.month && r.day == date.day,
      );
      selectData.record = record;
      
      // 检查是否是预测日
      selectData.isMenstruationForecastDay = _isInMenstruationForecast(date);
      selectData.isOvulationForecastDay = _isInOvulationForecast(date);
      
      data.add(selectData);
    }
    
    calendarData.value = data;
    _updateCurrentWeekData();
  }

  /// 更新当前周数据
  void _updateCurrentWeekData() {
    final selectedDateTime = selectedDate.value;
    final weekStart = selectedDateTime.subtract(Duration(days: selectedDateTime.weekday - 1));
    
    final weekData = calendarData.where((data) {
      final dataDate = data.dateTime;
      return dataDate.isAfter(weekStart.subtract(const Duration(days: 1))) &&
             dataDate.isBefore(weekStart.add(const Duration(days: 7)));
    }).toList();
    
    currentWeekData.value = weekData;
  }

  /// 生成卡片数据
  void _generateCardData() {
    final List<CardData> cards = [];
    
    // 月经期卡片
    if (_isInCurrentMenstruation()) {
      cards.add(CardData(
        title: '月经期',
        subtitle: '当前正在月经期',
        cardType: CardData.cardTypeMenstruation,
        topBgDrawableName: 'bg_menstruation_card',
      ));
    }
    
    // 下次月经预测卡片
    final nextMenstruation = _getNextMenstruationDate();
    if (nextMenstruation != null) {
      final daysUntil = nextMenstruation.difference(DateTime.now()).inDays;
      cards.add(CardData(
        title: '下次月经',
        subtitle: '$daysUntil天后 (${_formatDate(nextMenstruation)})',
        cardType: CardData.cardTypeForecast,
        topBgDrawableName: 'bg_forecast_card',
      ));
    }
    
    // 排卵期预测卡片
    final nextOvulation = _getNextOvulationDate();
    if (nextOvulation != null) {
      final daysUntil = nextOvulation.difference(DateTime.now()).inDays;
      cards.add(CardData(
        title: '排卵期',
        subtitle: '$daysUntil天后 (${_formatDate(nextOvulation)})',
        cardType: CardData.cardTypeOvulation,
        topBgDrawableName: 'bg_ovulation_card',
      ));
    }
    
    cardDataList.value = cards;
  }

  /// 选择日期
  void selectDate(DateTime date) {
    selectedDate.value = date;
    _updateCurrentWeekData();
  }

  /// 添加或更新月经记录
  Future<void> addOrUpdateMenstrualRecord({
    required DateTime date,
    required bool isMenstruation,
    required int bleedingVolume,
    bool isDropletBleeding = false,
    int symptom = 0,
  }) async {
    try {
      isLoading.value = true;
      
      // 查找现有记录
      var existingRecord = await _womanHealthService.getMenstrualRecordByDate(
        currentUserId.value,
        date,
      );
      
      if (existingRecord != null) {
        // 更新现有记录
        existingRecord = existingRecord.copyWith(
          isMenstruation: isMenstruation,
          bleedingVolume: bleedingVolume,
          isDropletBleeding: isDropletBleeding,
          symptom: symptom,
        );
      } else {
        // 创建新记录
        existingRecord = MenstrualRecord(
          uid: currentUserId.value,
          time: date.millisecondsSinceEpoch,
          day: date.day,
          month: date.month,
          year: date.year,
          isMenstruation: isMenstruation,
          bleedingVolume: bleedingVolume,
          isDropletBleeding: isDropletBleeding,
          symptom: symptom,
        );
      }
      
      await _womanHealthService.saveMenstrualRecord(existingRecord);
      
      // 重新加载数据
      await _loadMenstrualRecords();
      await _startForecastCalculation();
      _generateCalendarData();
      
      Get.snackbar('成功', '记录已保存');
    } catch (e) {
      print('$_tag: 保存记录失败: $e');
      Get.snackbar('错误', '保存失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }

  /// 删除月经记录
  Future<void> deleteMenstrualRecord(DateTime date) async {
    try {
      final record = await _womanHealthService.getMenstrualRecordByDate(
        currentUserId.value,
        date,
      );
      
      if (record != null && record.id != null) {
        final success = await _womanHealthService.deleteMenstrualRecord(record.id!);
        if (success) {
          await _loadMenstrualRecords();
          await _startForecastCalculation();
          _generateCalendarData();
          Get.snackbar('成功', '记录已删除');
        } else {
          Get.snackbar('错误', '删除失败');
        }
      }
    } catch (e) {
      print('$_tag: 删除记录失败: $e');
      Get.snackbar('错误', '删除失败，请重试');
    }
  }

  /// 更新用户设置
  Future<void> updateUserSettings({
    int? menstrualLength,
    int? cycleLength,
    bool? menstrualNotification,
    bool? pregnancyNotification,
  }) async {
    try {
      if (menstrualLength != null) this.menstrualLength.value = menstrualLength;
      if (cycleLength != null) menstrualCycleLength.value = cycleLength;
      if (menstrualNotification != null) isMenstrualNotification.value = menstrualNotification;
      if (pregnancyNotification != null) isPregnancyNotification.value = pregnancyNotification;
      
      // 保存到本地存储
      // await SharedPreferences...
      
      // 重新计算预测
      await _startForecastCalculation();
      _generateCalendarData();
      _generateCardData();
      
      Get.snackbar('成功', '设置已更新');
    } catch (e) {
      print('$_tag: 更新设置失败: $e');
      Get.snackbar('错误', '更新失败，请重试');
    }
  }

  // ForecastCalculationListener 实现
  @override
  void onHistoryCycle(List<RecordHistoryData> historyDataList) {
    this.historyDataList.value = historyDataList;
  }

  @override
  void onMenstruationTime(List<RecordHistoryData> menstrualDataList, int cycle, int duration) {
    menstrualForecastList.value = menstrualDataList;
    _updateNextMenstruationInfo();
  }

  @override
  void onOvulationTime(List<RecordHistoryData> ovulationDataList, int cycle) {
    ovulationForecastList.value = ovulationDataList;
    _updateNextOvulationInfo();
  }

  // 私有辅助方法
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month && date1.day == date2.day;
  }

  bool _isInMenstruationForecast(DateTime date) {
    return menstrualForecastList.any((data) => data.containsDate(date));
  }

  bool _isInOvulationForecast(DateTime date) {
    return ovulationForecastList.any((data) => data.containsDate(date));
  }

  bool _isInCurrentMenstruation() {
    final now = DateTime.now();
    return menstrualForecastList.any((data) => 
      data.containsDate(now) && !data.isForecast);
  }

  DateTime? _getNextMenstruationDate() {
    final now = DateTime.now();
    for (final data in menstrualForecastList) {
      if (data.startDate.isAfter(now)) {
        return data.startDate;
      }
    }
    return null;
  }

  DateTime? _getNextOvulationDate() {
    final now = DateTime.now();
    for (final data in ovulationForecastList) {
      if (data.isOvulationCycle && data.startDate.isAfter(now)) {
        return data.startDate;
      }
    }
    return null;
  }

  void _updateNextMenstruationInfo() {
    final nextDate = _getNextMenstruationDate();
    if (nextDate != null) {
      nextMenstruationDate.value = _formatDate(nextDate);
      daysUntilNextMenstruation.value = nextDate.difference(DateTime.now()).inDays;
    }
  }

  void _updateNextOvulationInfo() {
    final nextDate = _getNextOvulationDate();
    if (nextDate != null) {
      nextOvulationDate.value = _formatDate(nextDate);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.month}月${date.day}日';
  }
}
