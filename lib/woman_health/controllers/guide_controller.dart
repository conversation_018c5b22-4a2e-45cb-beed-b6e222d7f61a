import 'package:get/get.dart';
import 'package:flutter/material.dart';
import '../models/models.dart';
import '../utils/data_validator.dart';

/// 引导页面控制器
class GuideController extends GetxController {
  static const String _tag = 'GuideController';

  // 引导步骤
  final RxInt currentStep = 0.obs;
  final RxInt totalSteps = 10.obs;

  // 用户输入数据
  final RxInt menstrualLength = 5.obs;
  final RxInt menstrualCycleLength = 28.obs;
  final Rx<DateTime?> lastMenstruationDate = Rx<DateTime?>(null);
  final RxBool isMenstrualNotification = true.obs;
  final RxBool isPregnancyNotification = false.obs;
  final RxInt userAge = 25.obs;

  // 选项数据
  final RxList<OptionData<int>> menstrualLengthOptions = <OptionData<int>>[].obs;
  final RxList<OptionData<int>> cycleLengthOptions = <OptionData<int>>[].obs;
  final RxList<OptionData<bool>> notificationOptions = <OptionData<bool>>[].obs;

  // 状态
  final RxBool isLoading = false.obs;
  final RxBool canProceed = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeOptions();
    _validateCurrentStep();
  }

  /// 初始化选项数据
  void _initializeOptions() {
    // 月经持续时间选项 (3-10天)
    menstrualLengthOptions.value = List.generate(8, (index) {
      final days = index + 3;
      return OptionData<int>(
        title: '$days天',
        value: days,
        isSelected: days == menstrualLength.value,
      );
    });

    // 月经周期长度选项 (21-35天)
    cycleLengthOptions.value = List.generate(15, (index) {
      final days = index + 21;
      return OptionData<int>(
        title: '$days天',
        value: days,
        isSelected: days == menstrualCycleLength.value,
      );
    });

    // 通知选项
    notificationOptions.value = [
      OptionData<bool>(
        title: '月经提醒',
        value: true,
        isSelected: isMenstrualNotification.value,
        description: '在月经期前提醒您',
      ),
      OptionData<bool>(
        title: '排卵期提醒',
        value: false,
        isSelected: isPregnancyNotification.value,
        description: '在排卵期前提醒您',
      ),
    ];
  }

  /// 下一步
  void nextStep() {
    if (!canProceed.value) {
      Get.snackbar('提示', '请完成当前步骤的设置');
      return;
    }

    if (currentStep.value < totalSteps.value - 1) {
      currentStep.value++;
      _validateCurrentStep();
    } else {
      _completeGuide();
    }
  }

  /// 上一步
  void previousStep() {
    if (currentStep.value > 0) {
      currentStep.value--;
      _validateCurrentStep();
    }
  }

  /// 跳过引导
  void skipGuide() {
    Get.dialog(
      AlertDialog(
        title: const Text('跳过设置'),
        content: const Text('跳过设置将使用默认配置，您可以稍后在设置中修改。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              _completeGuide();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 完成引导（公共方法）
  void completeGuide() {
    _completeGuide();
  }

  /// 设置月经持续时间
  void setMenstrualLength(int length) {
    menstrualLength.value = length;
    
    // 更新选项状态
    for (var option in menstrualLengthOptions) {
      option.isSelected = option.value == length;
    }
    menstrualLengthOptions.refresh();
    
    _validateCurrentStep();
  }

  /// 设置月经周期长度
  void setCycleLength(int length) {
    menstrualCycleLength.value = length;
    
    // 更新选项状态
    for (var option in cycleLengthOptions) {
      option.isSelected = option.value == length;
    }
    cycleLengthOptions.refresh();
    
    _validateCurrentStep();
  }

  /// 设置最后一次月经日期
  void setLastMenstruationDate(DateTime date) {
    lastMenstruationDate.value = date;
    _validateCurrentStep();
  }

  /// 设置通知选项
  void setNotificationOption(bool isMenstrual, bool enabled) {
    if (isMenstrual) {
      isMenstrualNotification.value = enabled;
    } else {
      isPregnancyNotification.value = enabled;
    }
    
    // 更新选项状态
    for (var option in notificationOptions) {
      if (option.value == isMenstrual) {
        option.isSelected = enabled;
      }
    }
    notificationOptions.refresh();
    
    _validateCurrentStep();
  }

  /// 设置用户年龄
  void setUserAge(int age) {
    userAge.value = age;
    _validateCurrentStep();
  }

  /// 验证当前步骤
  void _validateCurrentStep() {
    switch (currentStep.value) {
      case 0: // 欢迎页面
        canProceed.value = true;
        break;
      case 1: // 设置月经持续时间
        final validation = DataValidator.validateMenstruationDuration(menstrualLength.value);
        canProceed.value = validation.isValid;
        break;
      case 2: // 设置月经周期长度
        final validation = DataValidator.validateCycleLength(menstrualCycleLength.value);
        canProceed.value = validation.isValid;
        break;
      case 3: // 设置最后一次月经日期
        canProceed.value = lastMenstruationDate.value != null;
        break;
      case 4: // 设置通知选项
        canProceed.value = true; // 通知选项是可选的
        break;
      default:
        canProceed.value = true;
    }
  }

  /// 完成引导
  Future<void> _completeGuide() async {
    try {
      isLoading.value = true;

      // 验证所有数据
      final validationResults = _validateAllData();
      if (validationResults.any((result) => !result.isValid)) {
        final errors = validationResults
            .where((result) => !result.isValid)
            .expand((result) => result.errors)
            .join('\n');
        Get.snackbar('验证失败', errors);
        return;
      }

      // 保存用户设置到本地存储
      await _saveUserSettings();

      // 标记引导已完成
      await _markGuideCompleted();

      // 导航到主页面
      Get.offAllNamed('/health_main');

    } catch (e) {
      print('$_tag: 完成引导失败: $e');
      Get.snackbar('错误', '设置保存失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }

  /// 验证所有数据
  List<ValidationResult> _validateAllData() {
    final results = <ValidationResult>[];

    // 验证月经持续时间
    results.add(DataValidator.validateMenstruationDuration(menstrualLength.value));

    // 验证月经周期长度
    results.add(DataValidator.validateCycleLength(menstrualCycleLength.value));

    // 验证用户年龄
    results.add(DataValidator.validateAge(userAge.value));

    return results;
  }

  /// 保存用户设置
  Future<void> _saveUserSettings() async {
    // 这里应该保存到SharedPreferences或数据库
    // 暂时使用Get.find来获取主控制器并更新设置
    try {
      // await SharedPreferences.getInstance().then((prefs) {
      //   prefs.setInt('menstrual_length', menstrualLength.value);
      //   prefs.setInt('menstrual_cycle_length', menstrualCycleLength.value);
      //   prefs.setBool('menstrual_notification', isMenstrualNotification.value);
      //   prefs.setBool('pregnancy_notification', isPregnancyNotification.value);
      //   prefs.setInt('user_age', userAge.value);
      //   if (lastMenstruationDate.value != null) {
      //     prefs.setString('last_menstruation_date', lastMenstruationDate.value!.toIso8601String());
      //   }
      // });
      
      print('$_tag: 用户设置已保存');
    } catch (e) {
      print('$_tag: 保存用户设置失败: $e');
      rethrow;
    }
  }

  /// 标记引导已完成
  Future<void> _markGuideCompleted() async {
    try {
      // await SharedPreferences.getInstance().then((prefs) {
      //   prefs.setBool('guide_completed', true);
      // });
      
      print('$_tag: 引导已标记为完成');
    } catch (e) {
      print('$_tag: 标记引导完成失败: $e');
      rethrow;
    }
  }

  /// 获取当前步骤标题
  String getCurrentStepTitle() {
    switch (currentStep.value) {
      case 0:
        return '经期时间线';
      case 1:
        return '上次月经来潮什么时候开始?';
      case 2:
        return '你一般每次月经持续多久?';
      case 3:
        return '你的月经周期通常是多久?';
      case 4:
        return '选项';
      case 5:
        return '经期时间线 - 已记录的行经日';
      case 6:
        return '经期时间线 - 已记录的详细信息';
      case 7:
        return '经期时间线 - 预测的详细信息';
      case 8:
        return '经期时间线 - 预测基于平均值';
      case 9:
        return '设置完成';
      default:
        return '设置完成';
    }
  }

  /// 获取当前步骤描述
  String getCurrentStepDescription() {
    switch (currentStep.value) {
      case 0:
        return '经期时间线设置';
      case 1:
        return '选择您最后一次月经的开始日期';
      case 2:
        return '月经长度从出血第一天算起，至下次月经前一天结束为止。';
      case 3:
        return '周期从月经第一天算起，至下次月经前一天结束\n预估周期长度有助于千预供月经预测。';
      case 4:
        return '选择您希望接收的提醒通知';
      case 5:
        return '红色圆圈表明你已记录的行经日。';
      case 6:
        return '紫色小圆圈表明你记录的头痛、经痛或测试结果等信息的时间';
      case 7:
        return '浅红色圆圈表明你可能出现月经的时间。';
      case 8:
        return '这些色卡表明你可能出现排卵期的时间，该期间为容易受孕。';
      case 9:
        return '设置已完成，开始使用吧！';
      default:
        return '设置已完成，开始使用吧！';
    }
  }

  /// 获取进度百分比
  double getProgress() {
    return (currentStep.value + 1) / totalSteps.value;
  }
}
