import 'package:json_annotation/json_annotation.dart';

part 'option_data.g.dart';

/// 选项数据模型
/// 用于各种选项选择功能
@JsonSerializable(genericArgumentFactories: true)
class OptionData<T> {
  /// 选项标题
  @<PERSON><PERSON><PERSON><PERSON>(name: 'title')
  String title;

  /// 选项值
  @Json<PERSON><PERSON>(name: 'value')
  T value;

  /// 是否选中
  @JsonKey(name: 'isSelected')
  bool isSelected;

  /// 是否启用
  @<PERSON>sonKey(name: 'isEnabled')
  bool isEnabled;

  /// 图标资源名称
  @<PERSON>sonKey(name: 'iconName')
  String? iconName;

  /// 描述信息
  @Json<PERSON>ey(name: 'description')
  String? description;

  /// 额外数据
  @Json<PERSON>ey(name: 'extra')
  Map<String, dynamic>? extra;

  OptionData({
    required this.title,
    required this.value,
    this.isSelected = false,
    this.isEnabled = true,
    this.iconName,
    this.description,
    this.extra,
  });

  /// 复制对象
  OptionData<T> copyWith({
    String? title,
    T? value,
    bool? isSelected,
    bool? isEnabled,
    String? iconName,
    String? description,
    Map<String, dynamic>? extra,
  }) {
    return OptionData<T>(
      title: title ?? this.title,
      value: value ?? this.value,
      isSelected: isSelected ?? this.isSelected,
      isEnabled: isEnabled ?? this.isEnabled,
      iconName: iconName ?? this.iconName,
      description: description ?? this.description,
      extra: extra ?? this.extra,
    );
  }

  /// JSON序列化
  factory OptionData.fromJson(
    Map<String, dynamic> json,
    T Function(Object? json) fromJsonT,
  ) =>
      _$OptionDataFromJson(json, fromJsonT);

  /// JSON反序列化
  Map<String, dynamic> toJson(Object? Function(T value) toJsonT) =>
      _$OptionDataToJson(this, toJsonT);

  @override
  String toString() {
    return 'OptionData{title: $title, value: $value, isSelected: $isSelected, isEnabled: $isEnabled}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OptionData &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          value == other.value;

  @override
  int get hashCode => title.hashCode ^ value.hashCode;
}

/// 症状选项数据
class SymptomOptionData extends OptionData<int> {
  SymptomOptionData({
    required super.title,
    required int symptomType,
    super.isSelected,
    super.iconName,
    super.description,
  }) : super(
          value: symptomType,
        );

  /// 症状类型
  int get symptomType => value;

  /// 创建症状选项列表
  static List<SymptomOptionData> createSymptomOptions() {
    return [
      SymptomOptionData(
        title: '食欲变化',
        symptomType: 0x01,
        iconName: 'ic_appetite',
        description: '食欲增加或减少',
      ),
      SymptomOptionData(
        title: '乳房疼痛',
        symptomType: 0x02,
        iconName: 'ic_breast_pain',
        description: '乳房胀痛或敏感',
      ),
      SymptomOptionData(
        title: '睡眠变化',
        symptomType: 0x04,
        iconName: 'ic_sleep',
        description: '失眠或嗜睡',
      ),
      SymptomOptionData(
        title: '腹部绞痛',
        symptomType: 0x08,
        iconName: 'ic_abdominal_pain',
        description: '腹部疼痛或痉挛',
      ),
      SymptomOptionData(
        title: '寒战',
        symptomType: 0x10,
        iconName: 'ic_chill',
        description: '感觉寒冷或发抖',
      ),
      SymptomOptionData(
        title: '腹泻',
        symptomType: 0x20,
        iconName: 'ic_diarrhea',
        description: '腹泻或肠胃不适',
      ),
      SymptomOptionData(
        title: '疲劳',
        symptomType: 0x40,
        iconName: 'ic_fatigue',
        description: '感觉疲倦或无力',
      ),
      SymptomOptionData(
        title: '头痛',
        symptomType: 0x80,
        iconName: 'ic_headache',
        description: '头痛或偏头痛',
      ),
      SymptomOptionData(
        title: '情绪变化',
        symptomType: 0x100,
        iconName: 'ic_mood',
        description: '情绪波动或易怒',
      ),
      SymptomOptionData(
        title: '恶心',
        symptomType: 0x200,
        iconName: 'ic_nausea',
        description: '恶心或想吐',
      ),
    ];
  }
}

/// 出血量选项数据
class BleedingVolumeOptionData extends OptionData<int> {
  BleedingVolumeOptionData({
    required super.title,
    required int volume,
    super.isSelected,
    super.iconName,
    super.description,
  }) : super(
          value: volume,
        );

  /// 出血量
  int get volume => value;

  /// 创建出血量选项列表
  static List<BleedingVolumeOptionData> createBleedingVolumeOptions() {
    return [
      BleedingVolumeOptionData(
        title: '未出血',
        volume: 0x00,
        iconName: 'ic_no_bleeding',
        description: '没有出血',
      ),
      BleedingVolumeOptionData(
        title: '少量',
        volume: 0x01,
        iconName: 'ic_light_bleeding',
        description: '轻微出血',
      ),
      BleedingVolumeOptionData(
        title: '中量',
        volume: 0x02,
        iconName: 'ic_medium_bleeding',
        description: '中等出血',
      ),
      BleedingVolumeOptionData(
        title: '多量',
        volume: 0x04,
        iconName: 'ic_heavy_bleeding',
        description: '大量出血',
      ),
    ];
  }
}
