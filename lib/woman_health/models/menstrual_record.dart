import 'package:json_annotation/json_annotation.dart';

part 'menstrual_record.g.dart';

/// 月经记录模型
/// 基于Android版本的MenstrualRecord实体
@JsonSerializable()
class MenstrualRecord {
  // 症状类型常量
  static const int symptomsTypeAppetiteChanges = 0x01; // 食欲变化
  static const int symptomsTypeBreastPain = 0x02; // 乳房疼痛
  static const int symptomsTypeSleepChanges = 0x04; // 睡眠变化
  static const int symptomsTypeAbdominalColic = 0x08; // 腹部绞痛
  static const int symptomsTypeChill = 0x10; // 寒战
  static const int symptomsTypeDiarrhea = 0x20; // 腹泻
  static const int symptomsTypeFatigue = 0x40; // 疲劳
  static const int symptomsTypeHeadache = 0x80; // 头痛
  static const int symptomsTypeMoodChanges = 0x100; // 情绪变化
  static const int symptomsTypeNausea = 0x200; // 恶心

  // 症状集合
  static const List<int> symptomsTypeArray = [
    symptomsTypeAppetiteChanges,
    symptomsTypeBreastPain,
    symptomsTypeSleepChanges,
    symptomsTypeAbdominalColic,
    symptomsTypeChill,
    symptomsTypeDiarrhea,
    symptomsTypeFatigue,
    symptomsTypeHeadache,
    symptomsTypeMoodChanges,
    symptomsTypeNausea,
  ];

  // 出血量常量
  static const int bleedingVolumeAbsence = 0x00; // 未出血
  static const int bleedingVolumeSmall = 0x01; // 少量
  static const int bleedingVolumeMedium = 0x02; // 中量
  static const int bleedingVolumeMany = 0x04; // 多量

  /// 唯一ID
  @JsonKey(name: 'id')
  int? id;

  /// 用户唯一ID
  @JsonKey(name: 'uid')
  String uid;

  /// 开始时间（时间戳）
  @JsonKey(name: 'time')
  int time;

  /// 日
  @JsonKey(name: 'day')
  int day;

  /// 月
  @JsonKey(name: 'month')
  int month;

  /// 年
  @JsonKey(name: 'year')
  int year;

  /// 症状（两个字节）
  @JsonKey(name: 'symptom')
  int symptom;

  /// 原始数据
  @JsonKey(name: 'data')
  List<int> data;

  /// 点滴出血
  @JsonKey(name: 'isDropletBleeding')
  bool isDropletBleeding;

  /// 是否月经
  @JsonKey(name: 'isMenstruation')
  bool isMenstruation;

  /// 出血量
  @JsonKey(name: 'bleedingVolume', defaultValue: 0)
  int bleedingVolume;

  MenstrualRecord({
    this.id,
    required this.uid,
    required this.time,
    required this.day,
    required this.month,
    required this.year,
    this.symptom = 0,
    this.data = const [],
    this.isDropletBleeding = false,
    this.isMenstruation = false,
    this.bleedingVolume = 0,
  });

  /// 设置时间
  void setTime(int year, int month, int day) {
    this.year = year;
    this.month = month;
    this.day = day;
    // 计算时间戳
    final dateTime = DateTime(year, month, day);
    time = dateTime.millisecondsSinceEpoch;
  }

  /// 获取DateTime对象
  DateTime get dateTime => DateTime.fromMillisecondsSinceEpoch(time);

  /// 检查是否有特定症状
  bool hasSymptom(int symptomType) {
    return (symptom & symptomType) != 0;
  }

  /// 添加症状
  void addSymptom(int symptomType) {
    symptom |= symptomType;
  }

  /// 移除症状
  void removeSymptom(int symptomType) {
    symptom &= ~symptomType;
  }

  /// 获取所有症状列表
  List<int> get symptomsList {
    List<int> symptoms = [];
    for (int symptomType in symptomsTypeArray) {
      if (hasSymptom(symptomType)) {
        symptoms.add(symptomType);
      }
    }
    return symptoms;
  }

  /// 获取症状名称
  static String getSymptomName(int symptomType) {
    switch (symptomType) {
      case symptomsTypeAppetiteChanges:
        return '食欲变化';
      case symptomsTypeBreastPain:
        return '乳房疼痛';
      case symptomsTypeSleepChanges:
        return '睡眠变化';
      case symptomsTypeAbdominalColic:
        return '腹部绞痛';
      case symptomsTypeChill:
        return '寒战';
      case symptomsTypeDiarrhea:
        return '腹泻';
      case symptomsTypeFatigue:
        return '疲劳';
      case symptomsTypeHeadache:
        return '头痛';
      case symptomsTypeMoodChanges:
        return '情绪变化';
      case symptomsTypeNausea:
        return '恶心';
      default:
        return '未知症状';
    }
  }

  /// 获取出血量名称
  static String getBleedingVolumeName(int volume) {
    switch (volume) {
      case bleedingVolumeAbsence:
        return '未出血';
      case bleedingVolumeSmall:
        return '少量';
      case bleedingVolumeMedium:
        return '中量';
      case bleedingVolumeMany:
        return '多量';
      default:
        return '未知';
    }
  }

  /// 获取出血量名称
  String get bleedingVolumeName => getBleedingVolumeName(bleedingVolume);

  /// 获取症状名称列表
  List<String> get symptomNames {
    return symptomsList.map((symptom) => getSymptomName(symptom)).toList();
  }

  /// 复制对象
  MenstrualRecord copyWith({
    int? id,
    String? uid,
    int? time,
    int? day,
    int? month,
    int? year,
    int? symptom,
    List<int>? data,
    bool? isDropletBleeding,
    bool? isMenstruation,
    int? bleedingVolume,
  }) {
    return MenstrualRecord(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      time: time ?? this.time,
      day: day ?? this.day,
      month: month ?? this.month,
      year: year ?? this.year,
      symptom: symptom ?? this.symptom,
      data: data ?? this.data,
      isDropletBleeding: isDropletBleeding ?? this.isDropletBleeding,
      isMenstruation: isMenstruation ?? this.isMenstruation,
      bleedingVolume: bleedingVolume ?? this.bleedingVolume,
    );
  }

  /// JSON序列化
  factory MenstrualRecord.fromJson(Map<String, dynamic> json) =>
      _$MenstrualRecordFromJson(json);

  /// JSON反序列化
  Map<String, dynamic> toJson() => _$MenstrualRecordToJson(this);

  @override
  String toString() {
    return 'MenstrualRecord{id: $id, uid: $uid, date: $year-$month-$day, '
        'isMenstruation: $isMenstruation, bleedingVolume: $bleedingVolume, '
        'symptom: $symptom, isDropletBleeding: $isDropletBleeding}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MenstrualRecord &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          uid == other.uid &&
          time == other.time;

  @override
  int get hashCode => id.hashCode ^ uid.hashCode ^ time.hashCode;
}
