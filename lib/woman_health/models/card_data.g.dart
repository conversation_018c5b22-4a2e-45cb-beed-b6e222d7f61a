// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'card_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CardData _$CardDataFromJson(Map<String, dynamic> json) => CardData(
      title: json['title'] as String,
      subtitle: json['subtitle'] as String? ?? '',
      topBgDrawableName: json['topBgDrawableName'] as String?,
      optionDatas: (json['optionDatas'] as List<dynamic>?)
          ?.map((e) => OptionData<String>.fromJson(
              e as Map<String, dynamic>, (value) => value as String))
          .toList(),
      isPrompt: json['isPrompt'] as bool? ?? false,
      promptStr: json['promptStr'] as String? ?? '',
      isAlone: json['isAlone'] as bool? ?? false,
      hemorrhageData: json['hemorrhageData'] == null
          ? null
          : HemorrhageData.fromJson(
              json['hemorrhageData'] as Map<String, dynamic>),
      cardType: (json['cardType'] as num?)?.toInt() ?? 0,
      extraData: json['extraData'] as Map<String, dynamic>?,
      isClickable: json['isClickable'] as bool? ?? true,
      clickAction: json['clickAction'] as String?,
    );

Map<String, dynamic> _$CardDataToJson(CardData instance) => <String, dynamic>{
      'title': instance.title,
      'subtitle': instance.subtitle,
      'topBgDrawableName': instance.topBgDrawableName,
      'optionDatas': instance.optionDatas
          ?.map((e) => e.toJson(
                (value) => value,
              ))
          .toList(),
      'isPrompt': instance.isPrompt,
      'promptStr': instance.promptStr,
      'isAlone': instance.isAlone,
      'hemorrhageData': instance.hemorrhageData,
      'cardType': instance.cardType,
      'extraData': instance.extraData,
      'isClickable': instance.isClickable,
      'clickAction': instance.clickAction,
    };
