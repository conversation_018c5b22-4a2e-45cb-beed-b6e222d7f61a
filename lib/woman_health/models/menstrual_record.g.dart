// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'menstrual_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MenstrualRecord _$MenstrualRecordFromJson(Map<String, dynamic> json) =>
    MenstrualRecord(
      id: (json['id'] as num?)?.toInt(),
      uid: json['uid'] as String,
      time: (json['time'] as num).toInt(),
      day: (json['day'] as num).toInt(),
      month: (json['month'] as num).toInt(),
      year: (json['year'] as num).toInt(),
      symptom: (json['symptom'] as num?)?.toInt() ?? 0,
      data: (json['data'] as List<dynamic>?)
              ?.map((e) => (e as num).toInt())
              .toList() ??
          const [],
      isDropletBleeding: json['isDropletBleeding'] as bool? ?? false,
      isMenstruation: json['isMenstruation'] as bool? ?? false,
      bleedingVolume: (json['bleedingVolume'] as num?)?.toInt() ?? 0,
    );

Map<String, dynamic> _$MenstrualRecordToJson(MenstrualRecord instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'time': instance.time,
      'day': instance.day,
      'month': instance.month,
      'year': instance.year,
      'symptom': instance.symptom,
      'data': instance.data,
      'isDropletBleeding': instance.isDropletBleeding,
      'isMenstruation': instance.isMenstruation,
      'bleedingVolume': instance.bleedingVolume,
    };
