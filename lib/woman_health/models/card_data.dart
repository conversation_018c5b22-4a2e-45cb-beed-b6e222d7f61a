import 'package:json_annotation/json_annotation.dart';
import 'option_data.dart';
import 'hemorrhage_data.dart';

part 'card_data.g.dart';

/// 卡片数据模型
/// 用于各种卡片显示
@JsonSerializable()
class CardData {
  /// 标题
  @J<PERSON><PERSON><PERSON>(name: 'title')
  String title;

  /// 副标题
  @Json<PERSON>ey(name: 'subtitle')
  String subtitle;

  /// 顶部背景图片资源名称
  @Json<PERSON><PERSON>(name: 'topBgDrawableName')
  String? topBgDrawableName;

  /// 选项数据列表
  @JsonKey(name: 'optionDatas')
  List<OptionData<String>>? optionDatas;

  /// 是否显示提示
  @Json<PERSON><PERSON>(name: 'isPrompt')
  bool isPrompt;

  /// 提示字符串
  @JsonKey(name: 'promptStr')
  String promptStr;

  /// 是否是独立卡片
  @JsonKey(name: 'isAlone')
  bool isAlone;

  /// 出血数据
  @<PERSON><PERSON><PERSON><PERSON>(name: 'hemorrhageData')
  HemorrhageData? hemorrhageData;

  /// 卡片类型
  @J<PERSON><PERSON><PERSON>(name: 'cardType', defaultValue: 0)
  int cardType;

  /// 额外数据
  @JsonKey(name: 'extraData')
  Map<String, dynamic>? extraData;

  /// 是否可点击
  @JsonKey(name: 'isClickable')
  bool isClickable;

  /// 点击动作类型
  @JsonKey(name: 'clickAction')
  String? clickAction;

  CardData({
    required this.title,
    this.subtitle = '',
    this.topBgDrawableName,
    this.optionDatas,
    this.isPrompt = false,
    this.promptStr = '',
    this.isAlone = false,
    this.hemorrhageData,
    this.cardType = 0,
    this.extraData,
    this.isClickable = true,
    this.clickAction,
  });

  /// 卡片类型常量
  static const int cardTypeDefault = 0; // 默认卡片
  static const int cardTypeMenstruation = 1; // 月经卡片
  static const int cardTypeOvulation = 2; // 排卵卡片
  static const int cardTypeForecast = 3; // 预测卡片
  static const int cardTypeSymptom = 4; // 症状卡片
  static const int cardTypeStatistics = 5; // 统计卡片
  static const int cardTypeSettings = 6; // 设置卡片

  /// 点击动作常量
  static const String actionNavigate = 'navigate'; // 导航
  static const String actionDialog = 'dialog'; // 显示对话框
  static const String actionSelect = 'select'; // 选择操作
  static const String actionEdit = 'edit'; // 编辑操作

  /// 获取卡片类型描述
  String get cardTypeDescription {
    switch (cardType) {
      case cardTypeDefault:
        return '默认卡片';
      case cardTypeMenstruation:
        return '月经卡片';
      case cardTypeOvulation:
        return '排卵卡片';
      case cardTypeForecast:
        return '预测卡片';
      case cardTypeSymptom:
        return '症状卡片';
      case cardTypeStatistics:
        return '统计卡片';
      case cardTypeSettings:
        return '设置卡片';
      default:
        return '未知卡片';
    }
  }

  /// 是否有选项数据
  bool get hasOptions => optionDatas != null && optionDatas!.isNotEmpty;

  /// 是否有出血数据
  bool get hasHemorrhageData => hemorrhageData != null;

  /// 是否有额外数据
  bool get hasExtraData => extraData != null && extraData!.isNotEmpty;

  /// 获取选中的选项
  List<OptionData<String>> get selectedOptions {
    if (!hasOptions) return [];
    return optionDatas!.where((option) => option.isSelected).toList();
  }

  /// 获取选中选项的值列表
  List<String> get selectedValues {
    return selectedOptions.map((option) => option.value).toList();
  }

  /// 设置选项选中状态
  void setOptionSelected(String value, bool selected) {
    if (!hasOptions) return;
    for (var option in optionDatas!) {
      if (option.value == value) {
        option.isSelected = selected;
        break;
      }
    }
  }

  /// 切换选项选中状态
  void toggleOptionSelected(String value) {
    if (!hasOptions) return;
    for (var option in optionDatas!) {
      if (option.value == value) {
        option.isSelected = !option.isSelected;
        break;
      }
    }
  }

  /// 清除所有选项选中状态
  void clearAllSelections() {
    if (!hasOptions) return;
    for (var option in optionDatas!) {
      option.isSelected = false;
    }
  }

  /// 复制对象
  CardData copyWith({
    String? title,
    String? subtitle,
    String? topBgDrawableName,
    List<OptionData<String>>? optionDatas,
    bool? isPrompt,
    String? promptStr,
    bool? isAlone,
    HemorrhageData? hemorrhageData,
    int? cardType,
    Map<String, dynamic>? extraData,
    bool? isClickable,
    String? clickAction,
  }) {
    return CardData(
      title: title ?? this.title,
      subtitle: subtitle ?? this.subtitle,
      topBgDrawableName: topBgDrawableName ?? this.topBgDrawableName,
      optionDatas: optionDatas ?? this.optionDatas,
      isPrompt: isPrompt ?? this.isPrompt,
      promptStr: promptStr ?? this.promptStr,
      isAlone: isAlone ?? this.isAlone,
      hemorrhageData: hemorrhageData ?? this.hemorrhageData,
      cardType: cardType ?? this.cardType,
      extraData: extraData ?? this.extraData,
      isClickable: isClickable ?? this.isClickable,
      clickAction: clickAction ?? this.clickAction,
    );
  }

  /// JSON序列化
  factory CardData.fromJson(Map<String, dynamic> json) =>
      _$CardDataFromJson(json);

  /// JSON反序列化
  Map<String, dynamic> toJson() => _$CardDataToJson(this);

  @override
  String toString() {
    return 'CardData{title: $title, subtitle: $subtitle, cardType: $cardTypeDescription, '
        'isPrompt: $isPrompt, isAlone: $isAlone, hasOptions: $hasOptions}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CardData &&
          runtimeType == other.runtimeType &&
          title == other.title &&
          cardType == other.cardType;

  @override
  int get hashCode => title.hashCode ^ cardType.hashCode;
}
