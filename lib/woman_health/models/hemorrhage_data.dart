import 'package:json_annotation/json_annotation.dart';

part 'hemorrhage_data.g.dart';

/// 出血数据模型
/// 用于出血量和出血类型的管理
@JsonSerializable()
class HemorrhageData {
  /// 出血量
  @J<PERSON><PERSON><PERSON>(name: 'volume')
  int volume;

  /// 是否点滴出血
  @J<PERSON><PERSON><PERSON>(name: 'isDropletBleeding')
  bool isDropletBleeding;

  /// 出血颜色
  @JsonKey(name: 'color')
  String? color;

  /// 出血质地
  @JsonKey(name: 'texture')
  String? texture;

  /// 出血持续时间（小时）
  @JsonKey(name: 'duration')
  int? duration;

  /// 备注
  @Json<PERSON>ey(name: 'note')
  String? note;

  /// 记录时间
  @Json<PERSON>ey(name: 'recordTime')
  int recordTime;

  HemorrhageData({
    required this.volume,
    this.isDropletBleeding = false,
    this.color,
    this.texture,
    this.duration,
    this.note,
    int? recordTime,
  }) : recordTime = recordTime ?? DateTime.now().millisecondsSinceEpoch;

  /// 出血量常量
  static const int volumeNone = 0x00; // 无出血
  static const int volumeLight = 0x01; // 少量
  static const int volumeMedium = 0x02; // 中量
  static const int volumeHeavy = 0x04; // 大量

  /// 出血颜色选项
  static const List<String> colorOptions = [
    '鲜红色',
    '暗红色',
    '褐色',
    '粉红色',
    '黑色',
  ];

  /// 出血质地选项
  static const List<String> textureOptions = [
    '液体状',
    '凝块状',
    '粘稠状',
    '丝状',
  ];

  /// 获取出血量描述
  String get volumeDescription {
    switch (volume) {
      case volumeNone:
        return '无出血';
      case volumeLight:
        return '少量';
      case volumeMedium:
        return '中量';
      case volumeHeavy:
        return '大量';
      default:
        return '未知';
    }
  }

  /// 是否有出血
  bool get hasBleeding => volume > volumeNone;

  /// 获取记录时间的DateTime对象
  DateTime get recordDateTime => DateTime.fromMillisecondsSinceEpoch(recordTime);

  /// 获取格式化的记录时间
  String get formattedRecordTime {
    final dateTime = recordDateTime;
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 复制对象
  HemorrhageData copyWith({
    int? volume,
    bool? isDropletBleeding,
    String? color,
    String? texture,
    int? duration,
    String? note,
    int? recordTime,
  }) {
    return HemorrhageData(
      volume: volume ?? this.volume,
      isDropletBleeding: isDropletBleeding ?? this.isDropletBleeding,
      color: color ?? this.color,
      texture: texture ?? this.texture,
      duration: duration ?? this.duration,
      note: note ?? this.note,
      recordTime: recordTime ?? this.recordTime,
    );
  }

  /// JSON序列化
  factory HemorrhageData.fromJson(Map<String, dynamic> json) =>
      _$HemorrhageDataFromJson(json);

  /// JSON反序列化
  Map<String, dynamic> toJson() => _$HemorrhageDataToJson(this);

  @override
  String toString() {
    return 'HemorrhageData{volume: $volumeDescription, isDropletBleeding: $isDropletBleeding, '
        'color: $color, texture: $texture, duration: $duration, recordTime: $formattedRecordTime}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HemorrhageData &&
          runtimeType == other.runtimeType &&
          volume == other.volume &&
          isDropletBleeding == other.isDropletBleeding &&
          color == other.color &&
          texture == other.texture &&
          recordTime == other.recordTime;

  @override
  int get hashCode =>
      volume.hashCode ^
      isDropletBleeding.hashCode ^
      color.hashCode ^
      texture.hashCode ^
      recordTime.hashCode;
}
