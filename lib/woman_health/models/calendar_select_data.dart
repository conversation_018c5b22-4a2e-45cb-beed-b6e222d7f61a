import 'package:json_annotation/json_annotation.dart';
import 'menstrual_record.dart';

part 'calendar_select_data.g.dart';

/// 日历选择数据模型
/// 用于日历选择功能
@JsonSerializable()
class CalendarSelectData {
  /// 年份
  @Json<PERSON><PERSON>(name: 'year')
  int year;

  /// 月份
  @Json<PERSON><PERSON>(name: 'month')
  int month;

  /// 日期
  @JsonKey(name: 'day')
  int day;

  /// 星期字符串
  @JsonKey(name: 'weekStr')
  String weekStr;

  /// 月经记录
  @JsonKey(name: 'record')
  MenstrualRecord? record;

  /// 是否是当前日期
  @JsonKey(name: 'isCurrentDay')
  bool isCurrentDay;

  /// 是否是排卵预测日
  @JsonKey(name: 'isOvulationForecastDay')
  bool isOvulationForecastDay;

  /// 是否是月经预测日
  @JsonKey(name: 'isMenstruationForecastDay')
  bool isMenstruationForecastDay;

  CalendarSelectData({
    required this.year,
    required this.month,
    required this.day,
    this.weekStr = '',
    this.record,
    this.isCurrentDay = false,
    this.isOvulationForecastDay = false,
    this.isMenstruationForecastDay = false,
  });

  /// 获取日期时间戳
  int get timeInMillis {
    return DateTime(year, month, day).millisecondsSinceEpoch;
  }

  /// 获取DateTime对象
  DateTime get dateTime => DateTime(year, month, day);

  /// 获取格式化的日期字符串
  String get formattedDate => '$year-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}';

  /// 获取星期几的中文名称
  String get weekdayName {
    final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    return weekdays[dateTime.weekday - 1];
  }

  /// 是否有月经记录
  bool get hasMenstrualRecord => record != null;

  /// 是否是月经日
  bool get isMenstruationDay => record?.isMenstruation ?? false;

  /// 是否有出血
  bool get hasBleeding => record != null && record!.bleedingVolume > MenstrualRecord.bleedingVolumeAbsence;

  /// 是否有症状记录
  bool get hasSymptoms => record != null && record!.symptom > 0;

  /// 是否是预测日（月经或排卵）
  bool get isForecastDay => isOvulationForecastDay || isMenstruationForecastDay;

  /// 复制对象
  CalendarSelectData copyWith({
    int? year,
    int? month,
    int? day,
    String? weekStr,
    MenstrualRecord? record,
    bool? isCurrentDay,
    bool? isOvulationForecastDay,
    bool? isMenstruationForecastDay,
  }) {
    return CalendarSelectData(
      year: year ?? this.year,
      month: month ?? this.month,
      day: day ?? this.day,
      weekStr: weekStr ?? this.weekStr,
      record: record ?? this.record,
      isCurrentDay: isCurrentDay ?? this.isCurrentDay,
      isOvulationForecastDay: isOvulationForecastDay ?? this.isOvulationForecastDay,
      isMenstruationForecastDay: isMenstruationForecastDay ?? this.isMenstruationForecastDay,
    );
  }

  /// JSON序列化
  factory CalendarSelectData.fromJson(Map<String, dynamic> json) =>
      _$CalendarSelectDataFromJson(json);

  /// JSON反序列化
  Map<String, dynamic> toJson() => _$CalendarSelectDataToJson(this);

  @override
  String toString() {
    return 'CalendarSelectData{date: $formattedDate, weekStr: $weekStr, '
        'isCurrentDay: $isCurrentDay, isOvulationForecastDay: $isOvulationForecastDay, '
        'isMenstruationForecastDay: $isMenstruationForecastDay, hasMenstrualRecord: $hasMenstrualRecord}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is CalendarSelectData &&
          runtimeType == other.runtimeType &&
          year == other.year &&
          month == other.month &&
          day == other.day;

  @override
  int get hashCode => year.hashCode ^ month.hashCode ^ day.hashCode;
}
