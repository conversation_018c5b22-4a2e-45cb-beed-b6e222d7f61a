import 'package:json_annotation/json_annotation.dart';

part 'record_history_data.g.dart';

/// 历史记录数据模型
/// 用于历史记录和预测显示
@JsonSerializable()
class RecordHistoryData {
  /// 开始时间戳
  @Json<PERSON><PERSON>(name: 'startTime')
  int startTime;

  /// 结束时间戳
  @Json<PERSON><PERSON>(name: 'endTime')
  int endTime;

  /// 周期类型（月经周期或排卵周期）
  @<PERSON>sonKey(name: 'cycleType')
  int cycleType;

  /// 周期长度（天数）
  @<PERSON>sonKey(name: 'cycleDays')
  int cycleDays;

  /// 持续时间（天数）
  @JsonKey(name: 'durationDays')
  int durationDays;

  /// 是否是预测数据
  @Json<PERSON>ey(name: 'isForecast')
  bool isForecast;

  /// 预测准确度（0-100）
  @JsonKey(name: 'accuracy')
  int? accuracy;

  /// 备注信息
  @JsonKey(name: 'note')
  String? note;

  RecordHistoryData({
    required this.startTime,
    required this.endTime,
    required this.cycleType,
    required this.cycleDays,
    required this.durationDays,
    this.isForecast = false,
    this.accuracy,
    this.note,
  });

  /// 周期类型常量
  static const int cycleTypeMenstruation = 1; // 月经周期
  static const int cycleTypeOvulation = 2; // 排卵周期
  static const int cycleTypeFertile = 3; // 易孕期

  /// 获取开始日期
  DateTime get startDate => DateTime.fromMillisecondsSinceEpoch(startTime);

  /// 获取结束日期
  DateTime get endDate => DateTime.fromMillisecondsSinceEpoch(endTime);

  /// 获取格式化的开始日期
  String get formattedStartDate {
    final date = startDate;
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 获取格式化的结束日期
  String get formattedEndDate {
    final date = endDate;
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 获取日期范围字符串
  String get dateRangeString => '$formattedStartDate - $formattedEndDate';

  /// 获取周期类型描述
  String get cycleTypeDescription {
    switch (cycleType) {
      case cycleTypeMenstruation:
        return isForecast ? '预测月经期' : '月经期';
      case cycleTypeOvulation:
        return isForecast ? '预测排卵期' : '排卵期';
      case cycleTypeFertile:
        return isForecast ? '预测易孕期' : '易孕期';
      default:
        return '未知周期';
    }
  }

  /// 是否是月经周期
  bool get isMenstruationCycle => cycleType == cycleTypeMenstruation;

  /// 是否是排卵周期
  bool get isOvulationCycle => cycleType == cycleTypeOvulation;

  /// 是否是易孕期
  bool get isFertileCycle => cycleType == cycleTypeFertile;

  /// 检查指定日期是否在此周期内
  bool containsDate(DateTime date) {
    final dateTime = DateTime(date.year, date.month, date.day);
    final start = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);
    
    return dateTime.isAtSameMomentAs(start) ||
           dateTime.isAtSameMomentAs(end) ||
           (dateTime.isAfter(start) && dateTime.isBefore(end));
  }

  /// 检查指定时间戳是否在此周期内
  bool containsTime(int timestamp) {
    return timestamp >= startTime && timestamp <= endTime;
  }

  /// 获取距离开始的天数
  int getDaysFromStart(DateTime date) {
    return date.difference(startDate).inDays;
  }

  /// 获取距离结束的天数
  int getDaysToEnd(DateTime date) {
    return endDate.difference(date).inDays;
  }

  /// 复制对象
  RecordHistoryData copyWith({
    int? startTime,
    int? endTime,
    int? cycleType,
    int? cycleDays,
    int? durationDays,
    bool? isForecast,
    int? accuracy,
    String? note,
  }) {
    return RecordHistoryData(
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      cycleType: cycleType ?? this.cycleType,
      cycleDays: cycleDays ?? this.cycleDays,
      durationDays: durationDays ?? this.durationDays,
      isForecast: isForecast ?? this.isForecast,
      accuracy: accuracy ?? this.accuracy,
      note: note ?? this.note,
    );
  }

  /// JSON序列化
  factory RecordHistoryData.fromJson(Map<String, dynamic> json) =>
      _$RecordHistoryDataFromJson(json);

  /// JSON反序列化
  Map<String, dynamic> toJson() => _$RecordHistoryDataToJson(this);

  @override
  String toString() {
    return 'RecordHistoryData{type: $cycleTypeDescription, dateRange: $dateRangeString, '
        'cycleDays: $cycleDays, durationDays: $durationDays, isForecast: $isForecast}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is RecordHistoryData &&
          runtimeType == other.runtimeType &&
          startTime == other.startTime &&
          endTime == other.endTime &&
          cycleType == other.cycleType;

  @override
  int get hashCode => startTime.hashCode ^ endTime.hashCode ^ cycleType.hashCode;
}
