// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'record_history_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RecordHistoryData _$RecordHistoryDataFromJson(Map<String, dynamic> json) =>
    RecordHistoryData(
      startTime: (json['startTime'] as num).toInt(),
      endTime: (json['endTime'] as num).toInt(),
      cycleType: (json['cycleType'] as num).toInt(),
      cycleDays: (json['cycleDays'] as num).toInt(),
      durationDays: (json['durationDays'] as num).toInt(),
      isForecast: json['isForecast'] as bool? ?? false,
      accuracy: (json['accuracy'] as num?)?.toInt(),
      note: json['note'] as String?,
    );

Map<String, dynamic> _$RecordHistoryDataToJson(RecordHistoryData instance) =>
    <String, dynamic>{
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'cycleType': instance.cycleType,
      'cycleDays': instance.cycleDays,
      'durationDays': instance.durationDays,
      'isForecast': instance.isForecast,
      'accuracy': instance.accuracy,
      'note': instance.note,
    };
