// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'hemorrhage_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HemorrhageData _$HemorrhageDataFromJson(Map<String, dynamic> json) =>
    HemorrhageData(
      volume: (json['volume'] as num).toInt(),
      isDropletBleeding: json['isDropletBleeding'] as bool? ?? false,
      color: json['color'] as String?,
      texture: json['texture'] as String?,
      duration: (json['duration'] as num?)?.toInt(),
      note: json['note'] as String?,
      recordTime: (json['recordTime'] as num?)?.toInt(),
    );

Map<String, dynamic> _$HemorrhageDataToJson(HemorrhageData instance) =>
    <String, dynamic>{
      'volume': instance.volume,
      'isDropletBleeding': instance.isDropletBleeding,
      'color': instance.color,
      'texture': instance.texture,
      'duration': instance.duration,
      'note': instance.note,
      'recordTime': instance.recordTime,
    };
