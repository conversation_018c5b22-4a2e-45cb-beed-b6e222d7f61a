import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';

part 'calendar_data.g.dart';

/// 日历数据模型
/// 用于日历显示功能
@JsonSerializable()
class CalendarData {
  /// 月份字符串
  @<PERSON><PERSON><PERSON><PERSON>(name: 'monthStr')
  String monthStr;

  /// 日期字符串
  @Json<PERSON>ey(name: 'dayStr')
  String dayStr;

  /// 是否选中
  @JsonKey(name: 'isSelect')
  bool isSelect;

  /// 是否是当前日期
  @JsonKey(name: 'isCurrentDay')
  bool isCurrentDay;

  /// 选中状态文字颜色
  @<PERSON>sonKey(name: 'daySelectTextColor')
  String daySelectTextColor;

  /// 未选中状态文字颜色
  @JsonKey(name: 'dayUnSelectTextColor')
  String dayUnSelectTextColor;

  /// 当前日期背景颜色资源ID（存储为字符串）
  @JsonKey(name: 'currentDayBgColor')
  String currentDayBgColor;

  /// 选中日期背景颜色资源ID（存储为字符串）
  @JsonKey(name: 'daySelectBgColor')
  String daySelectBgColor;

  CalendarData({
    this.monthStr = '',
    this.dayStr = '',
    this.isSelect = false,
    this.isCurrentDay = false,
    this.daySelectTextColor = '#FFFFFF',
    this.dayUnSelectTextColor = '#000000',
    this.currentDayBgColor = 'bg_circular_black_shape',
    this.daySelectBgColor = 'bg_circular_orange_shape',
  });

  /// 获取选中状态的文字颜色
  Color get selectTextColor => Color(int.parse(daySelectTextColor.replaceFirst('#', '0xFF')));

  /// 获取未选中状态的文字颜色
  Color get unSelectTextColor => Color(int.parse(dayUnSelectTextColor.replaceFirst('#', '0xFF')));

  /// 复制对象
  CalendarData copyWith({
    String? monthStr,
    String? dayStr,
    bool? isSelect,
    bool? isCurrentDay,
    String? daySelectTextColor,
    String? dayUnSelectTextColor,
    String? currentDayBgColor,
    String? daySelectBgColor,
  }) {
    return CalendarData(
      monthStr: monthStr ?? this.monthStr,
      dayStr: dayStr ?? this.dayStr,
      isSelect: isSelect ?? this.isSelect,
      isCurrentDay: isCurrentDay ?? this.isCurrentDay,
      daySelectTextColor: daySelectTextColor ?? this.daySelectTextColor,
      dayUnSelectTextColor: dayUnSelectTextColor ?? this.dayUnSelectTextColor,
      currentDayBgColor: currentDayBgColor ?? this.currentDayBgColor,
      daySelectBgColor: daySelectBgColor ?? this.daySelectBgColor,
    );
  }

  /// JSON序列化
  factory CalendarData.fromJson(Map<String, dynamic> json) =>
      _$CalendarDataFromJson(json);

  /// JSON反序列化
  Map<String, dynamic> toJson() => _$CalendarDataToJson(this);

  @override
  String toString() {
    return 'CalendarData{monthStr: $monthStr, dayStr: $dayStr, '
        'isSelect: $isSelect, isCurrentDay: $isCurrentDay}';
  }
}
