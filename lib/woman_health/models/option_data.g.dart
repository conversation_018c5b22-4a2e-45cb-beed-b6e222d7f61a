// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'option_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OptionData<T> _$OptionDataFromJson<T>(
  Map<String, dynamic> json,
  T Function(Object? json) fromJsonT,
) =>
    OptionData<T>(
      title: json['title'] as String,
      value: fromJsonT(json['value']),
      isSelected: json['isSelected'] as bool? ?? false,
      isEnabled: json['isEnabled'] as bool? ?? true,
      iconName: json['iconName'] as String?,
      description: json['description'] as String?,
      extra: json['extra'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$OptionDataToJson<T>(
  OptionData<T> instance,
  Object? Function(T value) toJsonT,
) =>
    <String, dynamic>{
      'title': instance.title,
      'value': toJsonT(instance.value),
      'isSelected': instance.isSelected,
      'isEnabled': instance.isEnabled,
      'iconName': instance.iconName,
      'description': instance.description,
      'extra': instance.extra,
    };
