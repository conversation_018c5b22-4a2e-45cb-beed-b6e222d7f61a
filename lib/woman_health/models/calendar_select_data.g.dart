// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calendar_select_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CalendarSelectData _$CalendarSelectDataFromJson(Map<String, dynamic> json) =>
    CalendarSelectData(
      year: (json['year'] as num).toInt(),
      month: (json['month'] as num).toInt(),
      day: (json['day'] as num).toInt(),
      weekStr: json['weekStr'] as String? ?? '',
      record: json['record'] == null
          ? null
          : MenstrualRecord.fromJson(json['record'] as Map<String, dynamic>),
      isCurrentDay: json['isCurrentDay'] as bool? ?? false,
      isOvulationForecastDay: json['isOvulationForecastDay'] as bool? ?? false,
      isMenstruationForecastDay:
          json['isMenstruationForecastDay'] as bool? ?? false,
    );

Map<String, dynamic> _$CalendarSelectDataToJson(CalendarSelectData instance) =>
    <String, dynamic>{
      'year': instance.year,
      'month': instance.month,
      'day': instance.day,
      'weekStr': instance.weekStr,
      'record': instance.record,
      'isCurrentDay': instance.isCurrentDay,
      'isOvulationForecastDay': instance.isOvulationForecastDay,
      'isMenstruationForecastDay': instance.isMenstruationForecastDay,
    };
