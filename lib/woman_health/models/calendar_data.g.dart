// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'calendar_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CalendarData _$CalendarDataFromJson(Map<String, dynamic> json) => CalendarData(
      monthStr: json['monthStr'] as String? ?? '',
      dayStr: json['dayStr'] as String? ?? '',
      isSelect: json['isSelect'] as bool? ?? false,
      isCurrentDay: json['isCurrentDay'] as bool? ?? false,
      daySelectTextColor: json['daySelectTextColor'] as String? ?? '#FFFFFF',
      dayUnSelectTextColor:
          json['dayUnSelectTextColor'] as String? ?? '#000000',
      currentDayBgColor:
          json['currentDayBgColor'] as String? ?? 'bg_circular_black_shape',
      daySelectBgColor:
          json['daySelectBgColor'] as String? ?? 'bg_circular_orange_shape',
    );

Map<String, dynamic> _$CalendarDataToJson(CalendarData instance) =>
    <String, dynamic>{
      'monthStr': instance.monthStr,
      'dayStr': instance.dayStr,
      'isSelect': instance.isSelect,
      'isCurrentDay': instance.isCurrentDay,
      'daySelectTextColor': instance.daySelectTextColor,
      'dayUnSelectTextColor': instance.dayUnSelectTextColor,
      'currentDayBgColor': instance.currentDayBgColor,
      'daySelectBgColor': instance.daySelectBgColor,
    };
