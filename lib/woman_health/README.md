# 女性健康功能模块

## 项目概述

本项目是基于Flutter和Dart语言实现的女性健康管理功能模块，移植自Android原生应用。该模块提供完整的月经周期跟踪、排卵期预测、症状记录和健康分析功能。

## 功能特性

### 核心功能
- 📅 **月经周期跟踪** - 记录和跟踪月经周期
- 🔮 **智能预测** - 基于历史数据预测月经期和排卵期
- 📊 **症状记录** - 记录和分析各种生理症状
- 📈 **数据统计** - 提供详细的健康数据分析
- 🔔 **智能提醒** - 月经期和排卵期提醒通知
- 📱 **直观界面** - 基于设计稿的美观用户界面

### 技术特性
- 🎯 **GetX状态管理** - 响应式状态管理和路由
- 🗄️ **本地数据库** - SQLite数据持久化存储
- 🧪 **单元测试** - 完整的测试覆盖
- 📐 **数据验证** - 严格的数据校验机制
- 🔄 **JSON序列化** - 自动化数据序列化

## 项目结构

```
lib/woman_health/
├── controllers/           # GetX控制器
│   ├── health_main_controller.dart
│   ├── guide_controller.dart
│   └── controllers.dart
├── models/               # 数据模型
│   ├── menstrual_record.dart
│   ├── calendar_data.dart
│   ├── option_data.dart
│   ├── hemorrhage_data.dart
│   ├── record_history_data.dart
│   ├── card_data.dart
│   └── models.dart
├── services/             # 业务服务
│   ├── woman_health_service.dart
│   ├── forecast_calculation.dart
│   ├── ovulation_calculator.dart
│   ├── symptom_analyzer.dart
│   ├── notification_calculator.dart
│   └── services.dart
├── database/             # 数据库相关
│   ├── dao/
│   │   └── menstrual_record_dao.dart
│   ├── helpers/
│   ├── models/
│   └── utils/
├── routes/               # 路由配置
│   ├── app_routes.dart
│   └── app_pages.dart
├── widgets/              # UI组件
│   └── custom_calendar.dart
├── utils/                # 工具类
│   └── data_validator.dart
└── woman_health.dart     # 主导出文件
```

## 核心组件

### 数据模型
- **MenstrualRecord** - 月经记录核心模型
- **CalendarData** - 日历显示数据
- **OptionData** - 选项数据（支持泛型）
- **HemorrhageData** - 出血数据
- **RecordHistoryData** - 历史记录数据
- **CardData** - 卡片显示数据

### 业务服务
- **WomanHealthService** - 核心健康服务
- **ForecastCalculation** - 预测计算引擎
- **OvulationCalculator** - 排卵期计算器
- **SymptomAnalyzer** - 症状分析器
- **NotificationCalculator** - 通知计算器

### 控制器
- **HealthMainController** - 主页面控制器
- **GuideController** - 引导页面控制器

## 数据库设计

### MenstrualRecord表
```sql
CREATE TABLE MenstrualRecord (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uid TEXT NOT NULL,
    time INTEGER NOT NULL,
    day INTEGER NOT NULL,
    month INTEGER NOT NULL,
    year INTEGER NOT NULL,
    symptom INTEGER DEFAULT 0,
    data BLOB,
    isDropletBleeding INTEGER DEFAULT 0,
    isMenstruation INTEGER DEFAULT 0,
    bleedingVolume INTEGER DEFAULT 0
);
```

## 使用方法

### 1. 导入模块
```dart
import 'package:ohos_app/woman_health/woman_health.dart';
```

### 2. 初始化控制器
```dart
// 在GetX应用中注册控制器
Get.put(HealthMainController());
```

### 3. 配置路由
```dart
GetMaterialApp(
  initialRoute: AppRoutes.healthMain,
  getPages: AppPages.routes,
)
```

### 4. 使用服务
```dart
final healthService = WomanHealthService();
final records = await healthService.getMenstrualRecords(userId);
```

## 预测算法

### 月经周期预测
基于用户历史数据，使用平均周期长度和持续时间进行预测：
- 分析最近6个月的月经记录
- 计算平均周期长度（21-35天）
- 计算平均持续时间（3-7天）
- 生成未来6个月的预测数据

### 排卵期计算
采用标准的排卵期计算方法：
- 排卵日 = 下次月经开始日期 - 14天
- 易孕期 = 排卵日前5天到排卵日后1天
- 支持基础体温和宫颈粘液辅助判断

### 症状分析
提供多维度症状分析：
- 症状出现频率统计
- 症状趋势分析
- 周期阶段症状关联分析
- 个性化健康建议

## 数据验证

### 验证规则
- 月经周期长度：21-35天
- 月经持续时间：1-10天
- 用户年龄：10-60岁
- 体温范围：35.0-42.0°C
- 日期有效性验证

### 验证结果
```dart
class ValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
}
```

## 通知系统

### 通知类型
- 月经期提醒
- 排卵期提醒
- 易孕期提醒
- 症状记录提醒

### 配置选项
- 提前天数设置
- 通知时间设置
- 重复间隔设置
- 个性化消息内容

## 测试

### 单元测试
- 数据模型测试
- 业务逻辑测试
- 数据验证测试
- 预测算法测试

### 运行测试
```bash
flutter test test/woman_health/
```

## 依赖项

### 核心依赖
- `get: ^4.6.6` - 状态管理和路由
- `sqflite: ^2.3.3+1` - 本地数据库
- `json_annotation: ^4.9.0` - JSON序列化

### 开发依赖
- `build_runner: ^2.4.11` - 代码生成
- `json_serializable: ^6.8.0` - JSON序列化生成
- `flutter_test` - 单元测试

## 性能优化

### 数据库优化
- 使用索引提高查询性能
- 批量操作减少数据库访问
- 懒加载大量数据

### 内存优化
- 使用GetX的懒加载控制器
- 及时释放不需要的资源
- 优化图片和资源加载

## UI界面实现

### 已完成的页面
- ✅ **主页面** (HealthMainView) - 完整的主界面，包含日历、预测卡片、快捷操作
- ✅ **引导页面** (GuideView) - 首次使用的设置引导流程
- ✅ **添加记录页面** (AddRecordView) - 月经记录和症状记录
- ✅ **预测页面** (ForecastView) - 月经和排卵期预测展示
- ✅ **自定义日历组件** (CustomCalendar) - 专业的月经日历组件

### UI组件库
- ✅ **通用组件** (CommonWidgets) - 按钮、输入框、卡片等基础组件
- ✅ **主题系统** - 完整的Material Design主题配置
- ✅ **响应式设计** - 基于ScreenUtil的屏幕适配

### 运行演示
```bash
# 运行女性健康功能演示
flutter run lib/woman_health_demo.dart
```

## 未来规划

- [ ] 添加数据导出功能
- [ ] 实现云端数据同步
- [ ] 增加更多症状类型
- [ ] 添加健康报告生成
- [ ] 支持多语言国际化
- [ ] 集成健康设备数据
- [ ] 完善剩余页面UI（设置、统计、历史等）

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 项目讨论区

---

**注意**: 本模块仅供健康跟踪参考，不能替代专业医疗建议。如有健康问题，请咨询专业医生。
