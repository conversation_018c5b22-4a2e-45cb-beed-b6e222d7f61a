import '../models/menstrual_record.dart';
import '../models/hemorrhage_data.dart';

/// 验证结果
class ValidationResult {
  /// 是否验证通过
  final bool isValid;
  
  /// 错误消息列表
  final List<String> errors;
  
  /// 警告消息列表
  final List<String> warnings;

  ValidationResult({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });

  /// 是否有错误
  bool get hasErrors => errors.isNotEmpty;
  
  /// 是否有警告
  bool get hasWarnings => warnings.isNotEmpty;
  
  /// 获取所有消息
  List<String> get allMessages => [...errors, ...warnings];

  @override
  String toString() {
    return 'ValidationResult{isValid: $isValid, errors: $errors, warnings: $warnings}';
  }
}

/// 数据验证器
class DataValidator {
  static const String _tag = 'DataValidator';

  /// 验证月经记录
  static ValidationResult validateMenstrualRecord(MenstrualRecord record) {
    final List<String> errors = [];
    final List<String> warnings = [];

    // 验证用户ID
    if (record.uid.isEmpty) {
      errors.add('用户ID不能为空');
    }

    // 验证日期
    final dateValidation = _validateDate(record.year, record.month, record.day);
    if (!dateValidation.isValid) {
      errors.addAll(dateValidation.errors);
    }

    // 验证时间戳与日期的一致性
    final expectedTime = DateTime(record.year, record.month, record.day).millisecondsSinceEpoch;
    final timeDiff = (record.time - expectedTime).abs();
    if (timeDiff > 86400000) { // 超过1天的差异
      warnings.add('时间戳与日期不一致');
    }

    // 验证出血量
    if (!_isValidBleedingVolume(record.bleedingVolume)) {
      errors.add('出血量值无效');
    }

    // 验证症状
    if (!_isValidSymptom(record.symptom)) {
      warnings.add('症状值可能无效');
    }

    // 验证月经标记与出血量的一致性
    if (record.isMenstruation && record.bleedingVolume == MenstrualRecord.bleedingVolumeAbsence) {
      warnings.add('标记为月经但出血量为零');
    }

    // 验证点滴出血与出血量的一致性
    if (record.isDropletBleeding && record.bleedingVolume > MenstrualRecord.bleedingVolumeSmall) {
      warnings.add('点滴出血但出血量较大');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证出血数据
  static ValidationResult validateHemorrhageData(HemorrhageData data) {
    final List<String> errors = [];
    final List<String> warnings = [];

    // 验证出血量
    if (!_isValidBleedingVolume(data.volume)) {
      errors.add('出血量值无效');
    }

    // 验证颜色
    if (data.color != null && !HemorrhageData.colorOptions.contains(data.color)) {
      warnings.add('出血颜色选项可能无效');
    }

    // 验证质地
    if (data.texture != null && !HemorrhageData.textureOptions.contains(data.texture)) {
      warnings.add('出血质地选项可能无效');
    }

    // 验证持续时间
    if (data.duration != null) {
      if (data.duration! < 0) {
        errors.add('持续时间不能为负数');
      } else if (data.duration! > 24) {
        warnings.add('持续时间超过24小时，请确认是否正确');
      }
    }

    // 验证记录时间
    final now = DateTime.now().millisecondsSinceEpoch;
    if (data.recordTime > now) {
      errors.add('记录时间不能是未来时间');
    }

    // 验证逻辑一致性
    if (data.volume == HemorrhageData.volumeNone && data.hasBleeding) {
      errors.add('出血量为零但标记为有出血');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证月经周期长度
  static ValidationResult validateCycleLength(int cycleLength) {
    final List<String> errors = [];
    final List<String> warnings = [];

    if (cycleLength < 21) {
      errors.add('月经周期长度不能少于21天');
    } else if (cycleLength > 35) {
      errors.add('月经周期长度不能超过35天');
    } else if (cycleLength < 24 || cycleLength > 32) {
      warnings.add('月经周期长度偏离正常范围(24-32天)');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证月经持续时间
  static ValidationResult validateMenstruationDuration(int duration) {
    final List<String> errors = [];
    final List<String> warnings = [];

    if (duration < 1) {
      errors.add('月经持续时间不能少于1天');
    } else if (duration > 10) {
      errors.add('月经持续时间不能超过10天');
    } else if (duration < 3 || duration > 7) {
      warnings.add('月经持续时间偏离正常范围(3-7天)');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证年龄
  static ValidationResult validateAge(int age) {
    final List<String> errors = [];
    final List<String> warnings = [];

    if (age < 10) {
      errors.add('年龄过小');
    } else if (age > 60) {
      warnings.add('年龄较大，请确认是否仍有月经');
    } else if (age < 12) {
      warnings.add('年龄较小，请确认是否已初潮');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证月经记录列表的连续性
  static ValidationResult validateRecordContinuity(List<MenstrualRecord> records) {
    final List<String> errors = [];
    final List<String> warnings = [];

    if (records.isEmpty) {
      return ValidationResult(isValid: true);
    }

    // 按时间排序
    final sortedRecords = List<MenstrualRecord>.from(records)
      ..sort((a, b) => a.time.compareTo(b.time));

    // 检查重复日期
    final Set<String> dateSet = {};
    for (final record in sortedRecords) {
      final dateKey = '${record.year}-${record.month}-${record.day}';
      if (dateSet.contains(dateKey)) {
        errors.add('存在重复日期记录: $dateKey');
      }
      dateSet.add(dateKey);
    }

    // 检查月经周期的合理性
    final menstrualRecords = sortedRecords.where((r) => r.isMenstruation).toList();
    if (menstrualRecords.length >= 2) {
      for (int i = 1; i < menstrualRecords.length; i++) {
        final current = menstrualRecords[i];
        final previous = menstrualRecords[i - 1];
        final daysDiff = current.dateTime.difference(previous.dateTime).inDays;
        
        if (daysDiff < 21) {
          warnings.add('月经周期过短: ${previous.formattedDate} 到 ${current.formattedDate} 仅 $daysDiff 天');
        } else if (daysDiff > 35) {
          warnings.add('月经周期过长: ${previous.formattedDate} 到 ${current.formattedDate} 有 $daysDiff 天');
        }
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证基础体温数据
  static ValidationResult validateTemperatureData(Map<DateTime, double> temperatureData) {
    final List<String> errors = [];
    final List<String> warnings = [];

    for (final entry in temperatureData.entries) {
      final date = entry.key;
      final temperature = entry.value;

      // 验证日期
      if (date.isAfter(DateTime.now())) {
        errors.add('体温记录日期不能是未来时间');
      }

      // 验证体温范围
      if (temperature < 35.0 || temperature > 42.0) {
        errors.add('体温值超出正常范围(35.0-42.0°C): ${temperature}°C');
      } else if (temperature < 36.0 || temperature > 37.5) {
        warnings.add('体温值偏离正常范围(36.0-37.5°C): ${temperature}°C');
      }
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证批量数据导入
  static ValidationResult validateBatchImport(List<MenstrualRecord> records) {
    final List<String> errors = [];
    final List<String> warnings = [];

    if (records.isEmpty) {
      errors.add('导入数据不能为空');
      return ValidationResult(isValid: false, errors: errors);
    }

    if (records.length > 1000) {
      warnings.add('导入数据量较大(${records.length}条)，处理可能需要较长时间');
    }

    // 验证每条记录
    int invalidCount = 0;
    for (int i = 0; i < records.length; i++) {
      final validation = validateMenstrualRecord(records[i]);
      if (!validation.isValid) {
        invalidCount++;
        if (invalidCount <= 5) { // 只显示前5个错误
          errors.add('第${i + 1}条记录: ${validation.errors.join(', ')}');
        }
      }
    }

    if (invalidCount > 5) {
      errors.add('还有${invalidCount - 5}条记录存在错误');
    }

    // 验证整体连续性
    final continuityValidation = validateRecordContinuity(records);
    errors.addAll(continuityValidation.errors);
    warnings.addAll(continuityValidation.warnings);

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }

  /// 验证日期
  static ValidationResult _validateDate(int year, int month, int day) {
    final List<String> errors = [];

    if (year < 1900 || year > DateTime.now().year + 1) {
      errors.add('年份无效: $year');
    }

    if (month < 1 || month > 12) {
      errors.add('月份无效: $month');
    }

    if (day < 1 || day > 31) {
      errors.add('日期无效: $day');
    }

    // 验证日期是否存在
    try {
      final date = DateTime(year, month, day);
      if (date.year != year || date.month != month || date.day != day) {
        errors.add('日期不存在: $year-$month-$day');
      }
    } catch (e) {
      errors.add('日期格式错误: $year-$month-$day');
    }

    return ValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
    );
  }

  /// 验证出血量是否有效
  static bool _isValidBleedingVolume(int volume) {
    return volume == MenstrualRecord.bleedingVolumeAbsence ||
           volume == MenstrualRecord.bleedingVolumeSmall ||
           volume == MenstrualRecord.bleedingVolumeMedium ||
           volume == MenstrualRecord.bleedingVolumeMany;
  }

  /// 验证症状是否有效
  static bool _isValidSymptom(int symptom) {
    if (symptom < 0) return false;
    
    // 检查是否只包含有效的症状位
    int validSymptoms = 0;
    for (final symptomType in MenstrualRecord.symptomsTypeArray) {
      validSymptoms |= symptomType;
    }
    
    return (symptom & ~validSymptoms) == 0;
  }
}

/// MenstrualRecord扩展，添加格式化日期方法
extension MenstrualRecordExtension on MenstrualRecord {
  String get formattedDate => '$year-${month.toString().padLeft(2, '0')}-${day.toString().padLeft(2, '0')}';
}
