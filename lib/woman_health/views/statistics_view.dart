import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/health_main_controller.dart';
import '../widgets/common_widgets.dart';
import '../models/models.dart';
import '../services/symptom_analyzer.dart';

/// 统计分析页面
class StatisticsView extends GetView<HealthMainController> {
  const StatisticsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(Icons.arrow_back, color: Color(0xFF333333)),
      ),
      title: Text(
        '统计分析',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildOverviewSection(),
          _buildCycleStatistics(),
          _buildSymptomStatistics(),
          _buildTrendAnalysis(),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  /// 构建概览部分
  Widget _buildOverviewSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '健康概览',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Obx(() => Row(
            children: [
              Expanded(
                child: _buildOverviewCard(
                  '记录天数',
                  '${controller.menstrualRecords.length}',
                  '天',
                  Icons.calendar_today,
                  const Color(0xFFFF6B9D),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildOverviewCard(
                  '平均周期',
                  '${controller.menstrualCycleLength.value}',
                  '天',
                  Icons.loop,
                  const Color(0xFF4CAF50),
                ),
              ),
            ],
          )),
          SizedBox(height: 12.h),
          Obx(() => Row(
            children: [
              Expanded(
                child: _buildOverviewCard(
                  '平均时长',
                  '${controller.menstrualLength.value}',
                  '天',
                  Icons.schedule,
                  const Color(0xFF2196F3),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildOverviewCard(
                  '症状记录',
                  '${_getSymptomRecordsCount()}',
                  '次',
                  Icons.healing,
                  const Color(0xFF9C27B0),
                ),
              ),
            ],
          )),
        ],
      ),
    );
  }

  /// 构建概览卡片
  Widget _buildOverviewCard(
    String title,
    String value,
    String unit,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 8.h),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: value,
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF333333),
                  ),
                ),
                TextSpan(
                  text: unit,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建周期统计
  Widget _buildCycleStatistics() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '周期统计',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          _buildCycleChart(),
          SizedBox(height: 16.h),
          _buildCycleDetails(),
        ],
      ),
    );
  }

  /// 构建周期图表
  Widget _buildCycleChart() {
    return Container(
      height: 120.h,
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFFFF6B9D).withOpacity(0.1),
            const Color(0xFF4CAF50).withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Column(
        children: [
          Text(
            '最近6个月周期变化',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF666666),
            ),
          ),
          SizedBox(height: 16.h),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: List.generate(6, (index) {
                final height = (50 + (index * 10) % 40).toDouble();
                return Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      width: 20.w,
                      height: height.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFFFF6B9D),
                        borderRadius: BorderRadius.circular(10.r),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '${index + 1}月',
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                );
              }),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建周期详情
  Widget _buildCycleDetails() {
    return Column(
      children: [
        _buildStatisticItem('最短周期', '26天', Icons.trending_down, const Color(0xFF2196F3)),
        _buildStatisticItem('最长周期', '32天', Icons.trending_up, const Color(0xFFFF9800)),
        _buildStatisticItem('周期变化', '±3天', Icons.swap_horiz, const Color(0xFF4CAF50)),
      ],
    );
  }

  /// 构建症状统计
  Widget _buildSymptomStatistics() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '症状统计',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Obx(() => _buildSymptomList()),
        ],
      ),
    );
  }

  /// 构建症状列表
  Widget _buildSymptomList() {
    final symptoms = [
      {'name': '头痛', 'frequency': 0.6, 'color': const Color(0xFFFF6B9D)},
      {'name': '疲劳', 'frequency': 0.8, 'color': const Color(0xFF4CAF50)},
      {'name': '腹痛', 'frequency': 0.4, 'color': const Color(0xFF2196F3)},
      {'name': '情绪变化', 'frequency': 0.7, 'color': const Color(0xFF9C27B0)},
      {'name': '乳房疼痛', 'frequency': 0.5, 'color': const Color(0xFFFF9800)},
    ];

    return Column(
      children: symptoms.map((symptom) {
        return Container(
          margin: EdgeInsets.only(bottom: 12.h),
          child: Row(
            children: [
              Container(
                width: 12.w,
                height: 12.h,
                decoration: BoxDecoration(
                  color: symptom['color'] as Color,
                  shape: BoxShape.circle,
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: Text(
                  symptom['name'] as String,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFF333333),
                  ),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${((symptom['frequency'] as double) * 100).toInt()}%',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: const Color(0xFF666666),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 4.h),
                    LinearProgressIndicator(
                      value: symptom['frequency'] as double,
                      backgroundColor: const Color(0xFFE5E5E5),
                      valueColor: AlwaysStoppedAnimation<Color>(
                        symptom['color'] as Color,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  /// 构建趋势分析
  Widget _buildTrendAnalysis() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '趋势分析',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          _buildTrendItem(
            '周期规律性',
            '良好',
            '您的月经周期相对规律，变化在正常范围内',
            Icons.check_circle,
            const Color(0xFF4CAF50),
          ),
          _buildTrendItem(
            '症状频率',
            '中等',
            '症状出现频率适中，建议继续观察记录',
            Icons.info,
            const Color(0xFFFF9800),
          ),
          _buildTrendItem(
            '整体健康',
            '优秀',
            '根据记录数据，您的生理健康状况良好',
            Icons.favorite,
            const Color(0xFFFF6B9D),
          ),
        ],
      ),
    );
  }

  /// 构建趋势项
  Widget _buildTrendItem(
    String title,
    String status,
    String description,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 16.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withOpacity(0.2)),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Icon(icon, color: color, size: 20.sp),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFF333333),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(12.r),
                      ),
                      child: Text(
                        status,
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 4.h),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatisticItem(String title, String value, IconData icon, Color color) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16.sp),
          SizedBox(width: 8.w),
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF666666),
            ),
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  /// 获取症状记录数量
  int _getSymptomRecordsCount() {
    return controller.menstrualRecords
        .where((record) => record.symptom > 0)
        .length;
  }
}
