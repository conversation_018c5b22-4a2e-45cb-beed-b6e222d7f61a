import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/health_main_controller.dart';
import '../widgets/common_widgets.dart';
import '../widgets/custom_calendar.dart';
import '../models/models.dart';

/// 女性健康主页面
class HealthMainView extends GetView<HealthMainController> {
  const HealthMainView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const LoadingIndicator(message: '加载中...');
        }
        return RefreshIndicator(
          onRefresh: controller.refreshData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            child: <PERSON>umn(
              children: [
                _buildHeader(),
                _buildCalendarSection(),
                _buildPredictionCards(),
                _buildQuickActions(),
                _buildRecentRecords(),
                <PERSON><PERSON><PERSON><PERSON>(height: 20.h),
              ],
            ),
          ),
        );
      }),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      title: Text(
        '女性健康',
        style: TextStyle(
          fontSize: 20.sp,
          fontWeight: FontWeight.bold,
          color: const Color(0xFF333333),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () => Get.toNamed('/settings'),
          icon: const Icon(
            Icons.settings_outlined,
            color: Color(0xFF666666),
          ),
        ),
        SizedBox(width: 8.w),
      ],
    );
  }

  /// 构建头部信息
  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          _buildCycleInfo(),
          SizedBox(height: 16.h),
          _buildNextPrediction(),
        ],
      ),
    );
  }

  /// 构建周期信息
  Widget _buildCycleInfo() {
    return Obx(() => Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildInfoItem(
          '当前周期',
          '第${controller.currentCycleDay.value}天',
          Icons.calendar_today,
          const Color(0xFFFF6B9D),
        ),
        _buildInfoItem(
          '平均周期',
          '${controller.menstrualCycleLength.value}天',
          Icons.loop,
          const Color(0xFF4CAF50),
        ),
        _buildInfoItem(
          '平均时长',
          '${controller.menstrualLength.value}天',
          Icons.schedule,
          const Color(0xFF2196F3),
        ),
      ],
    ));
  }

  /// 构建信息项
  Widget _buildInfoItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          width: 48.w,
          height: 48.h,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Icon(icon, color: color, size: 24.sp),
        ),
        SizedBox(height: 8.h),
        Text(
          value,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF333333),
          ),
        ),
        Text(
          title,
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  /// 构建下次预测
  Widget _buildNextPrediction() {
    return Obx(() => Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFF6B9D), Color(0xFFFF8A80)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '下次月经',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  controller.nextMenstruationDate.value.isNotEmpty
                      ? controller.nextMenstruationDate.value
                      : '计算中...',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                if (controller.daysUntilNextMenstruation.value > 0)
                  Text(
                    '还有${controller.daysUntilNextMenstruation.value}天',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
              ],
            ),
          ),
          Icon(
            Icons.favorite,
            color: Colors.white,
            size: 32.sp,
          ),
        ],
      ),
    ));
  }

  /// 构建日历部分
  Widget _buildCalendarSection() {
    return CardContainer(
      margin: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '月经日历',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF333333),
                ),
              ),
              TextButton(
                onPressed: () => Get.toNamed('/calendar'),
                child: Text(
                  '查看全部',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFFFF6B9D),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          Obx(() => CustomCalendar(
            calendarData: controller.calendarData,
            selectedDate: controller.selectedDate.value,
            onDateSelected: controller.selectDate,
            isWeekView: true,
            height: 120.h,
          )),
        ],
      ),
    );
  }

  /// 构建预测卡片
  Widget _buildPredictionCards() {
    return Obx(() {
      if (controller.cardDataList.isEmpty) {
        return const SizedBox.shrink();
      }
      
      return SizedBox(
        height: 160.h,
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          itemCount: controller.cardDataList.length,
          itemBuilder: (context, index) {
            final card = controller.cardDataList[index];
            return _buildPredictionCard(card);
          },
        ),
      );
    });
  }

  /// 构建预测卡片项
  Widget _buildPredictionCard(CardData card) {
    Color cardColor;
    IconData cardIcon;
    
    switch (card.cardType) {
      case CardData.cardTypeMenstruation:
        cardColor = const Color(0xFFFF6B9D);
        cardIcon = Icons.favorite;
        break;
      case CardData.cardTypeOvulation:
        cardColor = const Color(0xFF4CAF50);
        cardIcon = Icons.egg;
        break;
      case CardData.cardTypeForecast:
        cardColor = const Color(0xFF2196F3);
        cardIcon = Icons.schedule;
        break;
      default:
        cardColor = const Color(0xFF9C27B0);
        cardIcon = Icons.info;
    }

    return Container(
      width: 200.w,
      margin: EdgeInsets.only(right: 12.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [cardColor, cardColor.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(cardIcon, color: Colors.white, size: 24.sp),
              if (card.isPrompt)
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12.r),
                  ),
                  child: Text(
                    '提醒',
                    style: TextStyle(
                      fontSize: 10.sp,
                      color: Colors.white,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 12.h),
          Text(
            card.title,
            style: TextStyle(
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            card.subtitle,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white.withOpacity(0.9),
            ),
          ),
          const Spacer(),
          if (card.promptStr.isNotEmpty)
            Text(
              card.promptStr,
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.white.withOpacity(0.8),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建快捷操作
  Widget _buildQuickActions() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快捷操作',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildQuickActionItem(
                '记录月经',
                Icons.favorite,
                const Color(0xFFFF6B9D),
                () => controller.addMenstrualRecord(),
              ),
              _buildQuickActionItem(
                '添加症状',
                Icons.add_circle_outline,
                const Color(0xFF4CAF50),
                () => Get.toNamed('/add_symptom'),
              ),
              _buildQuickActionItem(
                '查看预测',
                Icons.insights,
                const Color(0xFF2196F3),
                () => Get.toNamed('/forecast'),
              ),
              _buildQuickActionItem(
                '历史记录',
                Icons.history,
                const Color(0xFF9C27B0),
                () => Get.toNamed('/history'),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建快捷操作项
  Widget _buildQuickActionItem(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56.w,
            height: 56.h,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Icon(icon, color: color, size: 28.sp),
          ),
          SizedBox(height: 8.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建最近记录
  Widget _buildRecentRecords() {
    return Obx(() {
      if (controller.menstrualRecords.isEmpty) {
        return const SizedBox.shrink();
      }

      final recentRecords = controller.menstrualRecords.take(3).toList();
      
      return CardContainer(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '最近记录',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF333333),
                  ),
                ),
                TextButton(
                  onPressed: () => Get.toNamed('/history'),
                  child: Text(
                    '查看全部',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFFFF6B9D),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 12.h),
            ...recentRecords.map((record) => _buildRecordItem(record)),
          ],
        ),
      );
    });
  }

  /// 构建记录项
  Widget _buildRecordItem(MenstrualRecord record) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Container(
            width: 8.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: record.isMenstruation 
                  ? const Color(0xFFFF6B9D) 
                  : const Color(0xFF4CAF50),
              borderRadius: BorderRadius.circular(4.r),
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${record.year}年${record.month}月${record.day}日',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF333333),
                  ),
                ),
                if (record.isMenstruation)
                  Text(
                    '月经期 - ${record.bleedingVolumeName}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                if (record.symptomNames.isNotEmpty)
                  Text(
                    '症状: ${record.symptomNames.join(', ')}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
              ],
            ),
          ),
          Icon(
            Icons.chevron_right,
            color: const Color(0xFFCCCCCC),
            size: 20.sp,
          ),
        ],
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () => Get.toNamed('/add_record'),
      backgroundColor: const Color(0xFFFF6B9D),
      child: const Icon(Icons.add, color: Colors.white),
    );
  }
}

/// HealthMainController扩展方法
extension HealthMainControllerExtension on HealthMainController {
  /// 刷新数据
  Future<void> refreshData() async {
    // await _loadInitialData();
  }

  /// 添加月经记录快捷方法
  void addMenstrualRecord() {
    final today = DateTime.now();
    addOrUpdateMenstrualRecord(
      date: today,
      isMenstruation: true,
      bleedingVolume: MenstrualRecord.bleedingVolumeSmall,
    );
  }
}
