import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/health_main_controller.dart';
import '../widgets/common_widgets.dart';
import '../widgets/custom_calendar.dart';
import '../models/models.dart';

/// 预测页面
class ForecastView extends GetView<HealthMainController> {
  const ForecastView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(Icons.arrow_back, color: Color(0xFF333333)),
      ),
      title: Text(
        '预测分析',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildPredictionSummary(),
          _buildCalendarView(),
          _buildPredictionDetails(),
          _buildAccuracyInfo(),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  /// 构建预测摘要
  Widget _buildPredictionSummary() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(24),
          bottomRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          _buildNextMenstruationCard(),
          SizedBox(height: 16.h),
          _buildNextOvulationCard(),
        ],
      ),
    );
  }

  /// 构建下次月经卡片
  Widget _buildNextMenstruationCard() {
    return Obx(() => Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFF6B9D), Color(0xFFFF8A80)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '下次月经预测',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  controller.nextMenstruationDate.value.isNotEmpty
                      ? controller.nextMenstruationDate.value
                      : '计算中...',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                if (controller.daysUntilNextMenstruation.value > 0)
                  Text(
                    '还有${controller.daysUntilNextMenstruation.value}天',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white.withOpacity(0.8),
                    ),
                  ),
              ],
            ),
          ),
          Icon(
            Icons.favorite,
            color: Colors.white,
            size: 32.sp,
          ),
        ],
      ),
    ));
  }

  /// 构建下次排卵卡片
  Widget _buildNextOvulationCard() {
    return Obx(() => Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF4CAF50), Color(0xFF81C784)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16.r),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '下次排卵预测',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withOpacity(0.9),
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  controller.nextOvulationDate.value.isNotEmpty
                      ? controller.nextOvulationDate.value
                      : '计算中...',
                  style: TextStyle(
                    fontSize: 20.sp,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                Text(
                  '易孕期提醒',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.egg,
            color: Colors.white,
            size: 32.sp,
          ),
        ],
      ),
    ));
  }

  /// 构建日历视图
  Widget _buildCalendarView() {
    return CardContainer(
      margin: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '预测日历',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Obx(() => CustomCalendar(
            calendarData: controller.calendarData,
            selectedDate: controller.selectedDate.value,
            onDateSelected: controller.selectDate,
            height: 320.h,
          )),
          SizedBox(height: 16.h),
          _buildCalendarLegend(),
        ],
      ),
    );
  }

  /// 构建日历图例
  Widget _buildCalendarLegend() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        _buildLegendItem('月经期', const Color(0xFFFF6B9D)),
        _buildLegendItem('预测月经', const Color(0xFFFFB3BA)),
        _buildLegendItem('排卵期', const Color(0xFF4CAF50)),
        _buildLegendItem('易孕期', const Color(0xFFA5D6A7)),
      ],
    );
  }

  /// 构建图例项
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12.w,
          height: 12.h,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 4.w),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  /// 构建预测详情
  Widget _buildPredictionDetails() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '预测详情',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Obx(() => Column(
            children: controller.menstrualForecastList.take(3).map((forecast) {
              return _buildForecastItem(forecast);
            }).toList(),
          )),
        ],
      ),
    );
  }

  /// 构建预测项
  Widget _buildForecastItem(RecordHistoryData forecast) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Container(
            width: 40.w,
            height: 40.h,
            decoration: BoxDecoration(
              color: forecast.isMenstruationCycle 
                  ? const Color(0xFFFF6B9D).withOpacity(0.1)
                  : const Color(0xFF4CAF50).withOpacity(0.1),
              borderRadius: BorderRadius.circular(20.r),
            ),
            child: Icon(
              forecast.isMenstruationCycle ? Icons.favorite : Icons.egg,
              color: forecast.isMenstruationCycle 
                  ? const Color(0xFFFF6B9D)
                  : const Color(0xFF4CAF50),
              size: 20.sp,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  forecast.cycleTypeDescription,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF333333),
                  ),
                ),
                Text(
                  forecast.dateRangeString,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
          if (forecast.accuracy != null)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
              decoration: BoxDecoration(
                color: _getAccuracyColor(forecast.accuracy!).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Text(
                '${forecast.accuracy}%',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: _getAccuracyColor(forecast.accuracy!),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// 构建准确度信息
  Widget _buildAccuracyInfo() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '预测准确度说明',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          _buildAccuracyItem(
            '高准确度 (85%+)',
            '基于充足的历史数据，预测较为可靠',
            const Color(0xFF4CAF50),
          ),
          _buildAccuracyItem(
            '中等准确度 (70-84%)',
            '有一定历史数据支撑，预测相对准确',
            const Color(0xFFFF9800),
          ),
          _buildAccuracyItem(
            '低准确度 (<70%)',
            '历史数据不足，预测仅供参考',
            const Color(0xFFF44336),
          ),
        ],
      ),
    );
  }

  /// 构建准确度项
  Widget _buildAccuracyItem(String title, String description, Color color) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      child: Row(
        children: [
          Container(
            width: 8.w,
            height: 8.h,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF333333),
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取准确度颜色
  Color _getAccuracyColor(int accuracy) {
    if (accuracy >= 85) {
      return const Color(0xFF4CAF50);
    } else if (accuracy >= 70) {
      return const Color(0xFFFF9800);
    } else {
      return const Color(0xFFF44336);
    }
  }
}
