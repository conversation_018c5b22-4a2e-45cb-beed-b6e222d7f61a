import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/health_main_controller.dart';
import '../widgets/common_widgets.dart';
import '../widgets/custom_calendar.dart';
import '../models/models.dart';

/// 日历详情页面
class CalendarView extends GetView<HealthMainController> {
  const CalendarView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(Icons.arrow_back, color: Color(0xFF333333)),
      ),
      title: Text(
        '健康日历',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () => _showCalendarLegend(),
          icon: const Icon(Icons.info_outline, color: Color(0xFF666666)),
        ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return Column(
      children: [
        _buildCalendarSection(),
        _buildSelectedDateInfo(),
        Expanded(child: _buildDayDetails()),
      ],
    );
  }

  /// 构建日历部分
  Widget _buildCalendarSection() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Obx(() => CustomCalendar(
            calendarData: controller.calendarData,
            selectedDate: controller.selectedDate.value,
            onDateSelected: controller.selectDate,
            height: 320.h,
          )),
          _buildCalendarLegend(),
          SizedBox(height: 16.h),
        ],
      ),
    );
  }

  /// 构建日历图例
  Widget _buildCalendarLegend() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 12.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildLegendItem('月经期', const Color(0xFFFF6B9D)),
          _buildLegendItem('预测月经', const Color(0xFFFFB3BA)),
          _buildLegendItem('排卵期', const Color(0xFF4CAF50)),
          _buildLegendItem('易孕期', const Color(0xFFA5D6A7)),
        ],
      ),
    );
  }

  /// 构建图例项
  Widget _buildLegendItem(String label, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12.w,
          height: 12.h,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 4.w),
        Text(
          label,
          style: TextStyle(
            fontSize: 12.sp,
            color: const Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  /// 构建选中日期信息
  Widget _buildSelectedDateInfo() {
    return Obx(() {
      final selectedDate = controller.selectedDate.value;
      final isToday = _isSameDay(selectedDate, DateTime.now());
      
      return Container(
        width: double.infinity,
        padding: EdgeInsets.all(16.w),
        margin: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatSelectedDate(selectedDate),
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF333333),
                  ),
                ),
                if (isToday)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFF6B9D),
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Text(
                      '今天',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: 8.h),
            _buildDateStatus(selectedDate),
          ],
        ),
      );
    });
  }

  /// 构建日期状态
  Widget _buildDateStatus(DateTime date) {
    // 这里应该根据实际数据判断日期状态
    final status = _getDateStatus(date);
    
    return Row(
      children: [
        Container(
          width: 8.w,
          height: 8.h,
          decoration: BoxDecoration(
            color: status['color'] as Color,
            shape: BoxShape.circle,
          ),
        ),
        SizedBox(width: 8.w),
        Text(
          status['text'] as String,
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF666666),
          ),
        ),
      ],
    );
  }

  /// 构建日期详情
  Widget _buildDayDetails() {
    return Obx(() {
      final selectedDate = controller.selectedDate.value;
      final record = _getRecordForDate(selectedDate);
      
      return SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            if (record != null) ...[
              _buildRecordCard(record),
              SizedBox(height: 16.h),
            ],
            _buildQuickActions(selectedDate),
            SizedBox(height: 16.h),
            _buildPredictionInfo(selectedDate),
            SizedBox(height: 100.h), // 为浮动按钮留空间
          ],
        ),
      );
    });
  }

  /// 构建记录卡片
  Widget _buildRecordCard(MenstrualRecord record) {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '当日记录',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF333333),
                ),
              ),
              IconButton(
                onPressed: () => _editRecord(record),
                icon: const Icon(Icons.edit, color: Color(0xFF666666)),
              ),
            ],
          ),
          SizedBox(height: 12.h),
          if (record.isMenstruation) ...[
            _buildRecordItem(
              '月经状态',
              '月经期',
              Icons.favorite,
              const Color(0xFFFF6B9D),
            ),
            _buildRecordItem(
              '出血量',
              record.bleedingVolumeName,
              Icons.opacity,
              const Color(0xFFFF6B9D),
            ),
          ],
          if (record.isDropletBleeding)
            _buildRecordItem(
              '点滴出血',
              '是',
              Icons.water_drop,
              const Color(0xFFFF9800),
            ),
          if (record.symptomNames.isNotEmpty) ...[
            _buildRecordItem(
              '症状',
              record.symptomNames.join(', '),
              Icons.healing,
              const Color(0xFF4CAF50),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建记录项
  Widget _buildRecordItem(String title, String value, IconData icon, Color color) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16.sp),
          SizedBox(width: 8.w),
          Text(
            title,
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF666666),
            ),
          ),
          SizedBox(width: 8.w),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF333333),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建快捷操作
  Widget _buildQuickActions(DateTime date) {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '快捷操作',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildActionButton(
                '记录月经',
                Icons.favorite,
                const Color(0xFFFF6B9D),
                () => _recordMenstruation(date),
              ),
              _buildActionButton(
                '添加症状',
                Icons.add_circle_outline,
                const Color(0xFF4CAF50),
                () => _addSymptom(date),
              ),
              _buildActionButton(
                '编辑记录',
                Icons.edit,
                const Color(0xFF2196F3),
                () => _editDateRecord(date),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton(
    String title,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56.w,
            height: 56.h,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(16.r),
            ),
            child: Icon(icon, color: color, size: 28.sp),
          ),
          SizedBox(height: 8.h),
          Text(
            title,
            style: TextStyle(
              fontSize: 12.sp,
              color: const Color(0xFF666666),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建预测信息
  Widget _buildPredictionInfo(DateTime date) {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '预测信息',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          _buildPredictionItem(
            '下次月经',
            '预计 3月15日 开始',
            Icons.favorite,
            const Color(0xFFFF6B9D),
          ),
          _buildPredictionItem(
            '排卵期',
            '预计 3月1日 开始',
            Icons.egg,
            const Color(0xFF4CAF50),
          ),
          _buildPredictionItem(
            '易孕期',
            '2月28日 - 3月3日',
            Icons.child_care,
            const Color(0xFF2196F3),
          ),
        ],
      ),
    );
  }

  /// 构建预测项
  Widget _buildPredictionItem(String title, String info, IconData icon, Color color) {
    return Container(
      margin: EdgeInsets.only(bottom: 12.h),
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 20.sp),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF333333),
                  ),
                ),
                Text(
                  info,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: () => _addRecordForSelectedDate(),
      backgroundColor: const Color(0xFFFF6B9D),
      child: const Icon(Icons.add, color: Colors.white),
    );
  }

  // 辅助方法
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  String _formatSelectedDate(DateTime date) {
    final weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    final weekday = weekdays[date.weekday % 7];
    return '${date.year}年${date.month}月${date.day}日 $weekday';
  }

  Map<String, dynamic> _getDateStatus(DateTime date) {
    // 这里应该根据实际数据判断
    return {
      'text': '无记录',
      'color': const Color(0xFFCCCCCC),
    };
  }

  MenstrualRecord? _getRecordForDate(DateTime date) {
    // 这里应该从控制器获取实际数据
    return controller.menstrualRecords.firstWhereOrNull(
      (record) => record.year == date.year &&
                  record.month == date.month &&
                  record.day == date.day,
    );
  }

  // 操作方法
  void _showCalendarLegend() {
    Get.dialog(
      AlertDialog(
        title: const Text('日历图例'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildLegendDialogItem('月经期', '实际月经记录', const Color(0xFFFF6B9D)),
            _buildLegendDialogItem('预测月经', '预测的月经期', const Color(0xFFFFB3BA)),
            _buildLegendDialogItem('排卵期', '预测的排卵期', const Color(0xFF4CAF50)),
            _buildLegendDialogItem('易孕期', '预测的易孕期', const Color(0xFFA5D6A7)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildLegendDialogItem(String title, String description, Color color) {
    return Container(
      margin: EdgeInsets.only(bottom: 8.h),
      child: Row(
        children: [
          Container(
            width: 16.w,
            height: 16.h,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: const Color(0xFF666666),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _editRecord(MenstrualRecord record) {
    Get.toNamed('/add_record');
  }

  void _recordMenstruation(DateTime date) {
    controller.addOrUpdateMenstrualRecord(
      date: date,
      isMenstruation: true,
      bleedingVolume: MenstrualRecord.bleedingVolumeSmall,
    );
  }

  void _addSymptom(DateTime date) {
    Get.toNamed('/add_symptom');
  }

  void _editDateRecord(DateTime date) {
    Get.toNamed('/add_record');
  }

  void _addRecordForSelectedDate() {
    Get.toNamed('/add_record');
  }
}
