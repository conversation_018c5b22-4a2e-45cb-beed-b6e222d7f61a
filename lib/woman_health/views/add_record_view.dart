import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/health_main_controller.dart';
import '../widgets/common_widgets.dart';
import '../models/models.dart';

/// 添加记录页面
class AddRecordView extends GetView<HealthMainController> {
  AddRecordView({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(Icons.close, color: Color(0xFF333333)),
      ),
      title: Text(
        '添加记录',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _saveRecord,
          child: Text(
            '保存',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFFFF6B9D),
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDateSection(),
          SizedBox(height: 24.h),
          _buildMenstruationSection(),
          SizedBox(height: 24.h),
          _buildBleedingSection(),
          SizedBox(height: 24.h),
          _buildSymptomsSection(),
          SizedBox(height: 24.h),
          _buildNotesSection(),
          SizedBox(height: 100.h), // 为底部按钮留空间
        ],
      ),
    );
  }

  /// 构建日期选择部分
  Widget _buildDateSection() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择日期',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Obx(() => CustomTextField(
            hintText: '请选择日期',
            readOnly: true,
            controller: TextEditingController(
              text: _formatDate(controller.selectedDate.value),
            ),
            suffixIcon: const Icon(Icons.calendar_today),
            onTap: _selectDate,
          )),
        ],
      ),
    );
  }

  /// 构建月经状态部分
  Widget _buildMenstruationSection() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '月经状态',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: _buildToggleButton(
                  '月经期',
                  true,
                  Icons.favorite,
                  const Color(0xFFFF6B9D),
                  () => _setMenstruationStatus(true),
                ),
              ),
              SizedBox(width: 12.w),
              Expanded(
                child: _buildToggleButton(
                  '非月经期',
                  false,
                  Icons.favorite_border,
                  const Color(0xFF4CAF50),
                  () => _setMenstruationStatus(false),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建出血量部分
  Widget _buildBleedingSection() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '出血量',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          _buildBleedingOptions(),
          SizedBox(height: 16.h),
          _buildDropletBleedingOption(),
        ],
      ),
    );
  }

  /// 构建出血量选项
  Widget _buildBleedingOptions() {
    final options = BleedingVolumeOptionData.createBleedingVolumeOptions();
    
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: options.map((option) => _buildBleedingChip(option)).toList(),
    );
  }

  /// 构建出血量芯片
  Widget _buildBleedingChip(BleedingVolumeOptionData option) {
    return Obx(() {
      final isSelected = _selectedBleedingVolume.value == option.volume;
      return FilterChip(
        label: Text(option.title),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            _selectedBleedingVolume.value = option.volume;
          }
        },
        selectedColor: const Color(0xFFFF6B9D).withOpacity(0.2),
        checkmarkColor: const Color(0xFFFF6B9D),
        labelStyle: TextStyle(
          color: isSelected ? const Color(0xFFFF6B9D) : const Color(0xFF666666),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      );
    });
  }

  /// 构建点滴出血选项
  Widget _buildDropletBleedingOption() {
    return Obx(() => CheckboxListTile(
      title: Text(
        '点滴出血',
        style: TextStyle(
          fontSize: 16.sp,
          color: const Color(0xFF333333),
        ),
      ),
      subtitle: Text(
        '少量不规律出血',
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF666666),
        ),
      ),
      value: _isDropletBleeding.value,
      onChanged: (value) => _isDropletBleeding.value = value ?? false,
      activeColor: const Color(0xFFFF6B9D),
      contentPadding: EdgeInsets.zero,
    ));
  }

  /// 构建症状部分
  Widget _buildSymptomsSection() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '症状记录',
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: const Color(0xFF333333),
                ),
              ),
              TextButton(
                onPressed: () => Get.toNamed('/add_symptom'),
                child: Text(
                  '详细记录',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: const Color(0xFFFF6B9D),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16.h),
          _buildSymptomOptions(),
        ],
      ),
    );
  }

  /// 构建症状选项
  Widget _buildSymptomOptions() {
    final options = SymptomOptionData.createSymptomOptions();
    
    return Wrap(
      spacing: 8.w,
      runSpacing: 8.h,
      children: options.take(6).map((option) => _buildSymptomChip(option)).toList(),
    );
  }

  /// 构建症状芯片
  Widget _buildSymptomChip(SymptomOptionData option) {
    return Obx(() {
      final isSelected = (_selectedSymptoms.value & option.symptomType) != 0;
      return FilterChip(
        label: Text(option.title),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            _selectedSymptoms.value |= option.symptomType;
          } else {
            _selectedSymptoms.value &= ~option.symptomType;
          }
        },
        selectedColor: const Color(0xFF4CAF50).withOpacity(0.2),
        checkmarkColor: const Color(0xFF4CAF50),
        labelStyle: TextStyle(
          color: isSelected ? const Color(0xFF4CAF50) : const Color(0xFF666666),
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
      );
    });
  }

  /// 构建备注部分
  Widget _buildNotesSection() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '备注',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          CustomTextField(
            hintText: '记录今天的感受或其他信息...',
            maxLines: 4,
            controller: _notesController,
          ),
        ],
      ),
    );
  }

  /// 构建切换按钮
  Widget _buildToggleButton(
    String text,
    bool value,
    IconData icon,
    Color color,
    VoidCallback onTap,
  ) {
    return Obx(() {
      final isSelected = _isMenstruation.value == value;
      return GestureDetector(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 16.h),
          decoration: BoxDecoration(
            color: isSelected ? color.withOpacity(0.1) : const Color(0xFFF8F9FA),
            borderRadius: BorderRadius.circular(12.r),
            border: Border.all(
              color: isSelected ? color : const Color(0xFFE5E5E5),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? color : const Color(0xFF999999),
                size: 24.sp,
              ),
              SizedBox(height: 8.h),
              Text(
                text,
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected ? color : const Color(0xFF666666),
                ),
              ),
            ],
          ),
        ),
      );
    });
  }

  /// 构建底部栏
  Widget _buildBottomBar() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: SecondaryButton(
              text: '取消',
              onPressed: () => Get.back(),
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Obx(() => PrimaryButton(
              text: '保存记录',
              onPressed: _saveRecord,
              isLoading: controller.isLoading.value,
            )),
          ),
        ],
      ),
    );
  }

  // 响应式变量
  final RxBool _isMenstruation = false.obs;
  final RxInt _selectedBleedingVolume = MenstrualRecord.bleedingVolumeAbsence.obs;
  final RxBool _isDropletBleeding = false.obs;
  final RxInt _selectedSymptoms = 0.obs;
  final TextEditingController _notesController = TextEditingController();

  /// 选择日期
  void _selectDate() async {
    final date = await showDatePicker(
      context: Get.context!,
      initialDate: controller.selectedDate.value,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFFFF6B9D),
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (date != null) {
      controller.selectDate(date);
    }
  }

  /// 设置月经状态
  void _setMenstruationStatus(bool isMenstruation) {
    _isMenstruation.value = isMenstruation;
    if (isMenstruation && _selectedBleedingVolume.value == MenstrualRecord.bleedingVolumeAbsence) {
      _selectedBleedingVolume.value = MenstrualRecord.bleedingVolumeSmall;
    }
  }

  /// 保存记录
  void _saveRecord() async {
    await controller.addOrUpdateMenstrualRecord(
      date: controller.selectedDate.value,
      isMenstruation: _isMenstruation.value,
      bleedingVolume: _selectedBleedingVolume.value,
      isDropletBleeding: _isDropletBleeding.value,
      symptom: _selectedSymptoms.value,
    );
    Get.back();
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}年${date.month}月${date.day}日';
  }
}
