import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/health_main_controller.dart';
import '../widgets/common_widgets.dart';

/// 设置页面
class SettingsView extends GetView<HealthMainController> {
  const SettingsView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8F9FA),
      appBar: _buildAppBar(),
      body: _buildBody(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        onPressed: () => Get.back(),
        icon: const Icon(Icons.arrow_back, color: Color(0xFF333333)),
      ),
      title: Text(
        '设置',
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w600,
          color: const Color(0xFF333333),
        ),
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      child: Column(
        children: [
          _buildProfileSection(),
          _buildCycleSettings(),
          _buildNotificationSettings(),
          _buildDataSettings(),
          _buildAboutSection(),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  /// 构建个人资料部分
  Widget _buildProfileSection() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '个人资料',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          _buildProfileItem(
            '头像',
            '',
            Icons.account_circle,
            () => _showAvatarDialog(),
            trailing: CircleAvatar(
              radius: 20.r,
              backgroundColor: const Color(0xFFFF6B9D),
              child: Icon(
                Icons.person,
                color: Colors.white,
                size: 20.sp,
              ),
            ),
          ),
          _buildProfileItem(
            '昵称',
            '用户',
            Icons.edit,
            () => _showNicknameDialog(),
          ),
          _buildProfileItem(
            '年龄',
            '25岁',
            Icons.cake,
            () => _showAgeDialog(),
          ),
        ],
      ),
    );
  }

  /// 构建周期设置
  Widget _buildCycleSettings() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '周期设置',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Obx(() => _buildSettingItem(
            '月经周期长度',
            '${controller.menstrualCycleLength.value}天',
            Icons.loop,
            () => _showCycleLengthDialog(),
          )),
          Obx(() => _buildSettingItem(
            '月经持续时间',
            '${controller.menstrualLength.value}天',
            Icons.schedule,
            () => _showDurationDialog(),
          )),
          _buildSettingItem(
            '最后一次月经',
            '2024年1月15日',
            Icons.calendar_today,
            () => _showLastPeriodDialog(),
          ),
        ],
      ),
    );
  }

  /// 构建通知设置
  Widget _buildNotificationSettings() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '通知设置',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          Obx(() => _buildSwitchItem(
            '月经提醒',
            '在月经期前提醒您',
            Icons.favorite,
            controller.isMenstrualNotification.value,
            (value) => controller.isMenstrualNotification.value = value,
          )),
          Obx(() => _buildSwitchItem(
            '排卵期提醒',
            '在排卵期前提醒您',
            Icons.egg,
            controller.isPregnancyNotification.value,
            (value) => controller.isPregnancyNotification.value = value,
          )),
          _buildSettingItem(
            '提醒时间',
            '上午 9:00',
            Icons.access_time,
            () => _showTimePickerDialog(),
          ),
          _buildSettingItem(
            '提前天数',
            '1天',
            Icons.notifications,
            () => _showAdvanceDaysDialog(),
          ),
        ],
      ),
    );
  }

  /// 构建数据设置
  Widget _buildDataSettings() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '数据管理',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          _buildSettingItem(
            '数据备份',
            '备份您的健康数据',
            Icons.backup,
            () => _showBackupDialog(),
          ),
          _buildSettingItem(
            '数据恢复',
            '从备份恢复数据',
            Icons.restore,
            () => _showRestoreDialog(),
          ),
          _buildSettingItem(
            '导出数据',
            '导出为Excel文件',
            Icons.file_download,
            () => _showExportDialog(),
          ),
          _buildSettingItem(
            '清除数据',
            '删除所有记录数据',
            Icons.delete_forever,
            () => _showClearDataDialog(),
            textColor: const Color(0xFFF44336),
          ),
        ],
      ),
    );
  }

  /// 构建关于部分
  Widget _buildAboutSection() {
    return CardContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '关于',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
          ),
          SizedBox(height: 16.h),
          _buildSettingItem(
            '版本信息',
            'v1.0.0',
            Icons.info,
            () => _showVersionDialog(),
          ),
          _buildSettingItem(
            '隐私政策',
            '查看隐私政策',
            Icons.privacy_tip,
            () => _showPrivacyDialog(),
          ),
          _buildSettingItem(
            '用户协议',
            '查看用户协议',
            Icons.description,
            () => _showTermsDialog(),
          ),
          _buildSettingItem(
            '帮助与反馈',
            '获取帮助或提供反馈',
            Icons.help,
            () => _showHelpDialog(),
          ),
        ],
      ),
    );
  }

  /// 构建个人资料项
  Widget _buildProfileItem(
    String title,
    String value,
    IconData icon,
    VoidCallback onTap, {
    Widget? trailing,
  }) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFFFF6B9D)),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16.sp,
          color: const Color(0xFF333333),
        ),
      ),
      subtitle: value.isNotEmpty
          ? Text(
              value,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
            )
          : null,
      trailing: trailing ??
          Icon(
            Icons.chevron_right,
            color: const Color(0xFFCCCCCC),
            size: 20.sp,
          ),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  /// 构建设置项
  Widget _buildSettingItem(
    String title,
    String value,
    IconData icon,
    VoidCallback onTap, {
    Color? textColor,
  }) {
    return ListTile(
      leading: Icon(icon, color: textColor ?? const Color(0xFFFF6B9D)),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16.sp,
          color: textColor ?? const Color(0xFF333333),
        ),
      ),
      subtitle: Text(
        value,
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF666666),
        ),
      ),
      trailing: Icon(
        Icons.chevron_right,
        color: const Color(0xFFCCCCCC),
        size: 20.sp,
      ),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  /// 构建开关项
  Widget _buildSwitchItem(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFFFF6B9D)),
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16.sp,
          color: const Color(0xFF333333),
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14.sp,
          color: const Color(0xFF666666),
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: const Color(0xFFFF6B9D),
      ),
      contentPadding: EdgeInsets.zero,
    );
  }

  // 对话框方法
  void _showAvatarDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('更换头像'),
        content: const Text('选择头像来源'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('相册'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('拍照'),
          ),
        ],
      ),
    );
  }

  void _showNicknameDialog() {
    final controller = TextEditingController(text: '用户');
    Get.dialog(
      AlertDialog(
        title: const Text('修改昵称'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            hintText: '请输入昵称',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showAgeDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('设置年龄'),
        content: const Text('请选择您的年龄'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showCycleLengthDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('设置周期长度'),
        content: const Text('请选择您的月经周期长度（21-35天）'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showDurationDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('设置持续时间'),
        content: const Text('请选择您的月经持续时间（1-10天）'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showLastPeriodDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('设置最后月经日期'),
        content: const Text('请选择您最后一次月经的开始日期'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showTimePickerDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('设置提醒时间'),
        content: const Text('请选择每日提醒时间'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showAdvanceDaysDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('设置提前天数'),
        content: const Text('请选择提前几天提醒'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showBackupDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('数据备份'),
        content: const Text('是否备份您的健康数据？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.snackbar('成功', '数据备份完成');
            },
            child: const Text('备份'),
          ),
        ],
      ),
    );
  }

  void _showRestoreDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('数据恢复'),
        content: const Text('是否从备份恢复数据？这将覆盖当前数据。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.snackbar('成功', '数据恢复完成');
            },
            child: const Text('恢复'),
          ),
        ],
      ),
    );
  }

  void _showExportDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('导出数据'),
        content: const Text('是否导出您的健康数据为Excel文件？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.snackbar('成功', '数据导出完成');
            },
            child: const Text('导出'),
          ),
        ],
      ),
    );
  }

  void _showClearDataDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('清除数据'),
        content: const Text('确定要删除所有记录数据吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Get.back();
              Get.snackbar('成功', '数据已清除');
            },
            child: const Text('确定', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showVersionDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('版本信息'),
        content: const Text('女性健康 v1.0.0\n\n基于Flutter开发的女性健康管理应用'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('隐私政策'),
        content: const Text('我们重视您的隐私，所有数据仅存储在本地设备中。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showTermsDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('用户协议'),
        content: const Text('请遵守用户协议，合理使用本应用。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('帮助与反馈'),
        content: const Text('如有问题或建议，请联系我们。'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}
