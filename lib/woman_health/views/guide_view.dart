import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import '../controllers/guide_controller.dart';

/// 引导页面主视图
class GuideView extends GetView<GuideController> {
  const GuideView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: _buildCurrentStep(),
      bottomNavigationBar: _buildBottomNavigation(),
    ));
  }

  /// 构建应用栏
  PreferredSizeWidget? _buildAppBar() {
    // 时间线步骤不显示应用栏
    if (controller.currentStep.value == 0 ||
        controller.currentStep.value >= 5) {
      return null;
    }

    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        onPressed: controller.previousStep,
        icon: const Icon(Icons.arrow_back, color: Color(0xFF333333)),
      ),
      actions: [
        if (controller.currentStep.value == 4) // 选项页面显示跳过按钮
          TextButton(
            onPressed: controller.skipGuide,
            child: Text(
              '跳过',
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
            ),
          ),
      ],
    );
  }

  /// 构建当前步骤内容
  Widget _buildCurrentStep() {
    switch (controller.currentStep.value) {
      
      case 0:
        return _buildLastPeriodStep();
      case 1:
        return _buildDurationStep();
      case 2:
        return _buildCycleStep();
      case 3:
        return _buildNotificationStep();
      case 4:
        return _buildTimelineStep();
      case 5:
        return _buildRecordedPeriodStep();
      case 6:
        return _buildRecordedDetailsStep();
      case 7:
        return _buildPredictedDetailsStep();
      case 8:
        return _buildPredictedAverageStep();
      case 9:
        return _buildFinalTimelineStep();
      default:
        return _buildTimelineStep();
    }
  }

  /// 构建时间线步骤
  Widget _buildTimelineStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '经期时间线',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          Text(
            '10月17日 今天',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Icon(
            Icons.keyboard_arrow_down,
            color: const Color(0xFF333333),
            size: 24.sp,
          ),
          SizedBox(height: 30.h),
          _buildWeekTimeline(),
          SizedBox(height: 30.h),
          _buildTimelineDescription(),
          SizedBox(height: 40.h),
          _buildTimelineInfoCard(),
          SizedBox(height: 100.h), // 为底部按钮留出空间
        ],
      ),
    );
  }

  /// 构建周时间线
  Widget _buildWeekTimeline() {
    final weekDays = ['六', '日', '一', '二', '三', '四', '五'];

    return Column(
      children: [
        // 星期标签
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: weekDays.map((day) => SizedBox(
            width: 40.w,
            child: Text(
              day,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
              textAlign: TextAlign.center,
            ),
          )).toList(),
        ),
        SizedBox(height: 16.h),
        // 时间线圆圈 - 根据当前步骤显示不同状态
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(7, (index) {
            return _buildTimelineCircle(index);
          }),
        ),
      ],
    );
  }

  /// 构建时间线圆圈
  Widget _buildTimelineCircle(int index) {
    // 根据当前步骤显示不同的时间线状态
    switch (controller.currentStep.value) {
      case 4: // 第一个时间线页面 - 红色圆圈（已记录的行经日）
        final isActive = index < 4;
        return Container(
          width: 40.w,
          height: 80.h,
          decoration: BoxDecoration(
            color: isActive ? const Color(0xFFFF6B6B) : const Color(0xFFE5E5E5),
            borderRadius: BorderRadius.circular(20.r),
          ),
        );
      case 5: // 最终时间线页面 - 根据UI截图显示不同状态
        return _buildFinalTimelineCircle(index);
      default: // 其他步骤 - 蓝色椭圆（预测基于平均值）
        final isActive = index < 4;
        return Container(
          width: 40.w,
          height: 80.h,
          decoration: BoxDecoration(
            color: isActive ? const Color(0xFF87CEEB) : const Color(0xFFE5E5E5),
            borderRadius: BorderRadius.circular(20.r),
          ),
        );
    }
  }

  /// 构建最终时间线圆圈（支持多种状态）
  Widget _buildFinalTimelineCircle(int index) {
    // 根据索引显示不同状态
    if (index < 4) {
      // 前4个显示红色圆圈 + 紫色小点
      return Column(
        children: [
          Container(
            width: 40.w,
            height: 80.h,
            decoration: BoxDecoration(
              color: const Color(0xFFFF6B6B),
              borderRadius: BorderRadius.circular(20.r),
            ),
          ),
          SizedBox(height: 8.h),
          Container(
            width: 8.w,
            height: 8.h,
            decoration: const BoxDecoration(
              color: Color(0xFF8A2BE2),
              shape: BoxShape.circle,
            ),
          ),
        ],
      );
    } else if (index < 6) {
      // 第5-6个显示浅红色圆圈（预测）
      return Container(
        width: 40.w,
        height: 80.h,
        decoration: BoxDecoration(
          color: const Color(0xFFFFB6C1),
          borderRadius: BorderRadius.circular(20.r),
        ),
      );
    } else {
      // 第7个显示灰色
      return Container(
        width: 40.w,
        height: 80.h,
        decoration: BoxDecoration(
          color: const Color(0xFFE5E5E5),
          borderRadius: BorderRadius.circular(20.r),
        ),
      );
    }
  }

  /// 构建时间线描述文字
  Widget _buildTimelineDescription() {
    switch (controller.currentStep.value) {
      case 4: // 第一个时间线页面
        return Text(
          '预测基于平均值',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF87CEEB),
          ),
          textAlign: TextAlign.center,
        );
      default:
        return Text(
          '预测基于平均值',
          style: TextStyle(
            fontSize: 14.sp,
            color: const Color(0xFF87CEEB),
          ),
          textAlign: TextAlign.center,
        );
    }
  }

  /// 构建时间线信息卡片
  Widget _buildTimelineInfoCard() {
    switch (controller.currentStep.value) {
      case 4: // 第一个时间线页面 - 已记录的行经日
        return Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            children: [
              Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFFF6B6B),
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '已记录的行经日',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF333333),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '紫红色圆圈表明你已记录的行经日。',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      case 5: // 最终时间线页面 - 已记录的详细信息
        return Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            children: [
              Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: const Color(0xFFFFB6C1),
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '已记录的详细信息',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF333333),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '浅红色圆圈表明你可能出现月经的时间。',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      default: // 其他步骤 - 预测基于平均值
        return Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: const Color(0xFFF5F5F5),
            borderRadius: BorderRadius.circular(12.r),
          ),
          child: Row(
            children: [
              Container(
                width: 40.w,
                height: 40.h,
                decoration: BoxDecoration(
                  color: const Color(0xFF87CEEB),
                  borderRadius: BorderRadius.circular(20.r),
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '已记录的详细信息',
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF333333),
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      '这些色卡表明你可能出现排卵期的时间，该期间为容易受孕。',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF666666),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
    }
  }

  /// 构建持续时间步骤
  Widget _buildDurationStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '你一般每次月经持续多久?',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 40.h),
          _buildDurationOptions(),
          SizedBox(height: 30.h),
          Text(
            '月经长度从出血第一天算起，至下次月经前一天结束为止。',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF666666),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 100.h), // 为底部按钮留出空间
        ],
      ),
    );
  }

  /// 构建持续时间选项
  Widget _buildDurationOptions() {
    final options = ['1天', '2天', '3天', '4天', '5天'];
    final selectedIndex = 2; // 根据UI截图，3天被选中

    return Column(
      children: options.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = index == selectedIndex;

        return Container(
          width: double.infinity,
          height: 50.h,
          margin: EdgeInsets.only(bottom: 12.h),
          child: ElevatedButton(
            onPressed: () => controller.setMenstrualLength(index + 1),
            style: ElevatedButton.styleFrom(
              backgroundColor: isSelected ? const Color(0xFFE5E5E5) : Colors.white,
              foregroundColor: const Color(0xFF333333),
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
                side: BorderSide(
                  color: const Color(0xFFE5E5E5),
                  width: 1,
                ),
              ),
            ),
            child: Text(
              option,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 构建周期步骤
  Widget _buildCycleStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '你的月经周期通常是多久?',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 40.h),
          _buildCycleOptions(),
          SizedBox(height: 30.h),
          Text(
            '周期从月经第一天算起，至下次月经前一天结束\n预估周期长度有助于千预供月经预测。',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF666666),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 100.h), // 为底部按钮留出空间
        ],
      ),
    );
  }

  /// 构建周期选项
  Widget _buildCycleOptions() {
    final options = ['26天', '27天', '28天', '29天', '30天'];
    final selectedIndex = 2; // 根据UI截图，28天被选中

    return Column(
      children: options.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = index == selectedIndex;

        return Container(
          width: double.infinity,
          height: 50.h,
          margin: EdgeInsets.only(bottom: 12.h),
          child: ElevatedButton(
            onPressed: () => controller.setCycleLength(26 + index),
            style: ElevatedButton.styleFrom(
              backgroundColor: isSelected ? const Color(0xFFE5E5E5) : Colors.white,
              foregroundColor: const Color(0xFF333333),
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12.r),
                side: const BorderSide(
                  color: Color(0xFFE5E5E5),
                  width: 1,
                ),
              ),
            ),
            child: Text(
              option,
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// 构建最后月经步骤
  Widget _buildLastPeriodStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '上次月经来潮什么时候开始?',
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          SizedBox(
            height: 400.h, // 固定高度给日历
            child: _buildCalendarView(),
          ),
          SizedBox(height: 100.h), // 为底部按钮留出空间
        ],
      ),
    );
  }

  /// 构建日历视图
  Widget _buildCalendarView() {
    return Column(
      children: [
        Text(
          '2023年10月',
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF333333),
          ),
        ),
        SizedBox(height: 20.h),
        // 星期标题
        Row(
          children: ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
              .map((day) => Expanded(
                    child: Text(
                      day,
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: const Color(0xFF666666),
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ))
              .toList(),
        ),
        SizedBox(height: 16.h),
        // 日历网格
        Expanded(
          child: GridView.builder(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: 1,
            ),
            itemCount: 35, // 5周
            itemBuilder: (context, index) {
              final day = index + 1;
              final isSelected = day == 17; // 根据UI截图，17号被选中

              if (day > 31) return const SizedBox.shrink();

              return GestureDetector(
                onTap: () => controller.setLastMenstruationDate(
                  DateTime(2023, 10, day),
                ),
                child: Container(
                  margin: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: isSelected ? const Color(0xFFFF6B35) : Colors.transparent,
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      day.toString(),
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected ? Colors.white :
                               day > 17 ? const Color(0xFFCCCCCC) : const Color(0xFF333333),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建通知步骤
  Widget _buildNotificationStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '选项',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          _buildNotificationOptions(),
          SizedBox(height: 100.h), // 为底部按钮留出空间
        ],
      ),
    );
  }

  /// 构建通知选项
  Widget _buildNotificationOptions() {
    final options = [
      {
        'title': '月经预测',
        'subtitle': '将使用你的数据来预测月经。',
        'enabled': true,
      },
      {
        'title': '月经通知',
        'subtitle': '将通知你临近月经来潮，并提示你记录。通知会在预测时间前天晚上8点发送。',
        'enabled': false,
      },
      {
        'title': '受孕窗口估算',
        'subtitle': '将使用你的数据来预测受孕窗口。',
        'enabled': true,
      },
      {
        'title': '受孕通知',
        'subtitle': '将通知你临近的受孕窗口。通知会在预测时间前天晚上8点发送。',
        'enabled': true,
      },
    ];

    return Column(
      children: options.map((option) => Container(
        margin: EdgeInsets.only(bottom: 16.h),
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: const Color(0xFFF5F5F5),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    option['title'] as String,
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFF333333),
                    ),
                  ),
                  SizedBox(height: 4.h),
                  Text(
                    option['subtitle'] as String,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF666666),
                    ),
                  ),
                ],
              ),
            ),
            Switch(
              value: option['enabled'] as bool,
              onChanged: (value) {
                // TODO: 实现开关切换逻辑
              },
              activeColor: const Color(0xFF007AFF),
            ),
          ],
        ),
      )).toList(),
    );
  }

  /// 构建最终时间线步骤
  Widget _buildFinalTimelineStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '经期时间线',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          Text(
            '10月17日 今天',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Icon(
            Icons.keyboard_arrow_down,
            color: const Color(0xFF333333),
            size: 24.sp,
          ),
          SizedBox(height: 30.h),
          _buildWeekTimeline(),
          SizedBox(height: 30.h),
          _buildTimelineDescription(),
          SizedBox(height: 40.h),
          _buildTimelineInfoCard(),
          SizedBox(height: 100.h), // 为底部按钮留出空间
        ],
      ),
    );
  }





  /// 构建底部导航
  Widget _buildBottomNavigation() {
    return Container(
      padding: EdgeInsets.all(24.w),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ElevatedButton(
        onPressed: () {
          if (controller.currentStep.value == 9) {
            // 最后一步，完成引导
            controller.completeGuide();
          } else {
            // 其他步骤，继续下一步
            controller.nextStep();
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFF6B35),
          foregroundColor: Colors.white,
          minimumSize: Size(double.infinity, 50.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12.r),
          ),
        ),
        child: Text(
          controller.currentStep.value == 9 ? '完成设置' : '下一步',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// 构建已记录行经日步骤（第一张图）
  Widget _buildRecordedPeriodStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '经期时间线',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          Text(
            '10月17日 今天',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Icon(
            Icons.keyboard_arrow_down,
            color: const Color(0xFF333333),
            size: 24.sp,
          ),
          SizedBox(height: 30.h),
          _buildRecordedPeriodTimeline(),
          SizedBox(height: 40.h),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                Container(
                  width: 40.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFF6B6B),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '已记录的行经日',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF333333),
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '红色圆圈表明你已记录的行经日。',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 100.h),
        ],
      ),
    );
  }

  /// 构建已记录行经日时间线
  Widget _buildRecordedPeriodTimeline() {
    final weekDays = ['六', '日', '一', '二', '三', '四', '五'];

    return Column(
      children: [
        // 星期标签
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: weekDays.map((day) => SizedBox(
            width: 40.w,
            child: Text(
              day,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
              textAlign: TextAlign.center,
            ),
          )).toList(),
        ),
        SizedBox(height: 16.h),
        // 时间线圆圈 - 红色圆圈表示已记录的行经日
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(7, (index) {
            // 前4个是红色（已记录的行经日），后3个是灰色
            final isActive = index < 4;
            return Container(
              width: 40.w,
              height: 80.h,
              decoration: BoxDecoration(
                color: isActive ? const Color(0xFFFF6B6B) : const Color(0xFFE5E5E5),
                borderRadius: BorderRadius.circular(20.r),
              ),
            );
          }),
        ),
      ],
    );
  }

  /// 构建已记录详细信息步骤（第二张图）
  Widget _buildRecordedDetailsStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '经期时间线',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          Text(
            '10月17日 今天',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Icon(
            Icons.keyboard_arrow_down,
            color: const Color(0xFF333333),
            size: 24.sp,
          ),
          SizedBox(height: 30.h),
          _buildRecordedDetailsTimeline(),
          SizedBox(height: 40.h),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                Container(
                  width: 40.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFFE5E5E5),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                  child: Center(
                    child: Container(
                      width: 8.w,
                      height: 8.h,
                      decoration: const BoxDecoration(
                        color: Color(0xFF8A2BE2),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '已记录的详细信息',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF333333),
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '紫色小圆圈表明你记录的头痛、经痛或测试结果等信息的时间',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 100.h),
        ],
      ),
    );
  }

  /// 构建已记录详细信息时间线
  Widget _buildRecordedDetailsTimeline() {
    final weekDays = ['六', '日', '一', '二', '三', '四', '五'];

    return Column(
      children: [
        // 星期标签
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: weekDays.map((day) => SizedBox(
            width: 40.w,
            child: Text(
              day,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
              textAlign: TextAlign.center,
            ),
          )).toList(),
        ),
        SizedBox(height: 16.h),
        // 时间线圆圈 - 红色圆圈 + 紫色小点
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(7, (index) {
            final isActive = index < 4;
            return Column(
              children: [
                Container(
                  width: 40.w,
                  height: 80.h,
                  decoration: BoxDecoration(
                    color: isActive ? const Color(0xFFFF6B6B) : const Color(0xFFE5E5E5),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                ),
                SizedBox(height: 8.h),
                // 紫色小点表示详细信息
                if (isActive)
                  Container(
                    width: 8.w,
                    height: 8.h,
                    decoration: const BoxDecoration(
                      color: Color(0xFF8A2BE2),
                      shape: BoxShape.circle,
                    ),
                  )
                else
                  SizedBox(height: 8.h),
              ],
            );
          }),
        ),
      ],
    );
  }

  /// 构建预测详细信息步骤（第三张图）
  Widget _buildPredictedDetailsStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '经期时间线',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          Text(
            '10月17日 今天',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Icon(
            Icons.keyboard_arrow_down,
            color: const Color(0xFF333333),
            size: 24.sp,
          ),
          SizedBox(height: 30.h),
          _buildPredictedDetailsTimeline(),
          SizedBox(height: 30.h),
          Text(
            '预测基于平均值',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF87CEEB),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 40.h),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                Container(
                  width: 40.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFFB6C1),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '已记录的详细信息',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF333333),
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '浅红色圆圈表明你可能出现月经的时间。',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 100.h),
        ],
      ),
    );
  }

  /// 构建预测详细信息时间线
  Widget _buildPredictedDetailsTimeline() {
    final weekDays = ['六', '日', '一', '二', '三', '四', '五'];

    return Column(
      children: [
        // 星期标签
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: weekDays.map((day) => SizedBox(
            width: 40.w,
            child: Text(
              day,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
              textAlign: TextAlign.center,
            ),
          )).toList(),
        ),
        SizedBox(height: 16.h),
        // 时间线圆圈 - 浅红色圆圈表示预测
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(7, (index) {
            final isActive = index < 6; // 前6个是浅红色
            return Container(
              width: 40.w,
              height: 80.h,
              decoration: BoxDecoration(
                color: isActive ? const Color(0xFFFFB6C1) : const Color(0xFFE5E5E5),
                borderRadius: BorderRadius.circular(20.r),
              ),
            );
          }),
        ),
      ],
    );
  }

  /// 构建预测基于平均值步骤（第四张图）
  Widget _buildPredictedAverageStep() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          Text(
            '经期时间线',
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 30.h),
          Text(
            '10月17日 今天',
            style: TextStyle(
              fontSize: 16.sp,
              color: const Color(0xFF333333),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Icon(
            Icons.keyboard_arrow_down,
            color: const Color(0xFF333333),
            size: 24.sp,
          ),
          SizedBox(height: 30.h),
          _buildPredictedAverageTimeline(),
          SizedBox(height: 30.h),
          Text(
            '预测基于平均值',
            style: TextStyle(
              fontSize: 14.sp,
              color: const Color(0xFF87CEEB),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 40.h),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
              color: const Color(0xFFF5F5F5),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Row(
              children: [
                Container(
                  width: 40.w,
                  height: 40.h,
                  decoration: BoxDecoration(
                    color: const Color(0xFF87CEEB),
                    borderRadius: BorderRadius.circular(20.r),
                  ),
                ),
                SizedBox(width: 16.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '已记录的详细信息',
                        style: TextStyle(
                          fontSize: 16.sp,
                          fontWeight: FontWeight.w600,
                          color: const Color(0xFF333333),
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '这些色卡表明你可能出现排卵期的时间，该期间为容易受孕。',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFF666666),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 100.h),
        ],
      ),
    );
  }

  /// 构建预测基于平均值时间线
  Widget _buildPredictedAverageTimeline() {
    final weekDays = ['六', '日', '一', '二', '三', '四', '五'];

    return Column(
      children: [
        // 星期标签
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: weekDays.map((day) => SizedBox(
            width: 40.w,
            child: Text(
              day,
              style: TextStyle(
                fontSize: 14.sp,
                color: const Color(0xFF666666),
              ),
              textAlign: TextAlign.center,
            ),
          )).toList(),
        ),
        SizedBox(height: 16.h),
        // 时间线圆圈 - 蓝色椭圆表示预测基于平均值
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: List.generate(7, (index) {
            final isActive = index < 4;
            return Container(
              width: 40.w,
              height: 80.h,
              decoration: BoxDecoration(
                color: isActive ? const Color(0xFF87CEEB) : const Color(0xFFE5E5E5),
                borderRadius: BorderRadius.circular(20.r),
              ),
            );
          }),
        ),
      ],
    );
  }
}
