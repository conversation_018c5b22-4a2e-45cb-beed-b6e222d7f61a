/// 女性健康应用路由定义
class AppRoutes {
  // 私有构造函数
  AppRoutes._();

  // 路由路径常量
  static const String initial = '/';
  static const String guide = '/guide';
  static const String healthMain = '/health_main';
  static const String addRecord = '/add_record';
  static const String addSymptom = '/add_symptom';
  static const String forecast = '/forecast';
  static const String settings = '/settings';
  static const String statistics = '/statistics';
  static const String calendar = '/calendar';
  static const String history = '/history';
  static const String notifications = '/notifications';
  static const String profile = '/profile';
  static const String about = '/about';
  static const String help = '/help';

  // 引导页面路由
  static const String guideWelcome = '/guide/welcome';
  static const String guideDuration = '/guide/duration';
  static const String guideCycle = '/guide/cycle';
  static const String guideLastPeriod = '/guide/last_period';
  static const String guideNotifications = '/guide/notifications';
  static const String guideComplete = '/guide/complete';

  // 设置页面路由
  static const String settingsGeneral = '/settings/general';
  static const String settingsNotifications = '/settings/notifications';
  static const String settingsCycle = '/settings/cycle';
  static const String settingsPrivacy = '/settings/privacy';
  static const String settingsBackup = '/settings/backup';

  // 记录页面路由
  static const String recordDetails = '/record/details';
  static const String recordEdit = '/record/edit';
  static const String symptomDetails = '/symptom/details';
  static const String symptomEdit = '/symptom/edit';

  // 预测页面路由
  static const String forecastMenstruation = '/forecast/menstruation';
  static const String forecastOvulation = '/forecast/ovulation';
  static const String forecastFertile = '/forecast/fertile';

  // 统计页面路由
  static const String statisticsOverview = '/statistics/overview';
  static const String statisticsSymptoms = '/statistics/symptoms';
  static const String statisticsCycle = '/statistics/cycle';
  static const String statisticsTrends = '/statistics/trends';

  /// 获取所有路由路径列表
  static List<String> getAllRoutes() {
    return [
      initial,
      guide,
      healthMain,
      addRecord,
      addSymptom,
      forecast,
      settings,
      statistics,
      calendar,
      history,
      notifications,
      profile,
      about,
      help,
      guideWelcome,
      guideDuration,
      guideCycle,
      guideLastPeriod,
      guideNotifications,
      guideComplete,
      settingsGeneral,
      settingsNotifications,
      settingsCycle,
      settingsPrivacy,
      settingsBackup,
      recordDetails,
      recordEdit,
      symptomDetails,
      symptomEdit,
      forecastMenstruation,
      forecastOvulation,
      forecastFertile,
      statisticsOverview,
      statisticsSymptoms,
      statisticsCycle,
      statisticsTrends,
    ];
  }

  /// 检查路由是否存在
  static bool isValidRoute(String route) {
    return getAllRoutes().contains(route);
  }

  /// 获取路由显示名称
  static String getRouteDisplayName(String route) {
    switch (route) {
      case initial:
        return '首页';
      case guide:
        return '引导设置';
      case healthMain:
        return '女性健康';
      case addRecord:
        return '添加记录';
      case addSymptom:
        return '添加症状';
      case forecast:
        return '预测';
      case settings:
        return '设置';
      case statistics:
        return '统计';
      case calendar:
        return '日历';
      case history:
        return '历史记录';
      case notifications:
        return '通知';
      case profile:
        return '个人资料';
      case about:
        return '关于';
      case help:
        return '帮助';
      case guideWelcome:
        return '欢迎';
      case guideDuration:
        return '设置持续时间';
      case guideCycle:
        return '设置周期';
      case guideLastPeriod:
        return '最后月经';
      case guideNotifications:
        return '通知设置';
      case guideComplete:
        return '设置完成';
      case settingsGeneral:
        return '通用设置';
      case settingsNotifications:
        return '通知设置';
      case settingsCycle:
        return '周期设置';
      case settingsPrivacy:
        return '隐私设置';
      case settingsBackup:
        return '备份设置';
      case recordDetails:
        return '记录详情';
      case recordEdit:
        return '编辑记录';
      case symptomDetails:
        return '症状详情';
      case symptomEdit:
        return '编辑症状';
      case forecastMenstruation:
        return '月经预测';
      case forecastOvulation:
        return '排卵预测';
      case forecastFertile:
        return '易孕期预测';
      case statisticsOverview:
        return '统计概览';
      case statisticsSymptoms:
        return '症状统计';
      case statisticsCycle:
        return '周期统计';
      case statisticsTrends:
        return '趋势分析';
      default:
        return '未知页面';
    }
  }

  /// 获取路由层级
  static int getRouteLevel(String route) {
    return route.split('/').length - 1;
  }

  /// 获取父路由
  static String? getParentRoute(String route) {
    final parts = route.split('/');
    if (parts.length <= 2) return null;
    
    parts.removeLast();
    return parts.join('/');
  }

  /// 获取子路由列表
  static List<String> getChildRoutes(String parentRoute) {
    return getAllRoutes()
        .where((route) => 
            route.startsWith(parentRoute) && 
            route != parentRoute &&
            route.split('/').length == parentRoute.split('/').length + 1)
        .toList();
  }

  /// 检查是否是引导页面路由
  static bool isGuideRoute(String route) {
    return route.startsWith('/guide');
  }

  /// 检查是否是设置页面路由
  static bool isSettingsRoute(String route) {
    return route.startsWith('/settings');
  }

  /// 检查是否是记录页面路由
  static bool isRecordRoute(String route) {
    return route.startsWith('/record') || route.startsWith('/symptom');
  }

  /// 检查是否是预测页面路由
  static bool isForecastRoute(String route) {
    return route.startsWith('/forecast');
  }

  /// 检查是否是统计页面路由
  static bool isStatisticsRoute(String route) {
    return route.startsWith('/statistics');
  }

  /// 获取路由图标
  static String getRouteIcon(String route) {
    switch (route) {
      case healthMain:
        return 'ic_home';
      case addRecord:
        return 'ic_add';
      case addSymptom:
        return 'ic_symptom';
      case forecast:
        return 'ic_forecast';
      case settings:
        return 'ic_settings';
      case statistics:
        return 'ic_statistics';
      case calendar:
        return 'ic_calendar';
      case history:
        return 'ic_history';
      case notifications:
        return 'ic_notifications';
      case profile:
        return 'ic_profile';
      default:
        return 'ic_default';
    }
  }
}
