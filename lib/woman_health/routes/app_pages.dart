import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'app_routes.dart';
import '../controllers/health_main_controller.dart';
import '../controllers/guide_controller.dart';
import '../views/health_main_view.dart';
import '../views/guide_view.dart';
import '../views/add_record_view.dart';
import '../views/forecast_view.dart';
import '../views/statistics_view.dart';
import '../views/settings_view.dart';
import '../views/calendar_view.dart';
import '../../main.dart';

/// GetX页面配置
class AppPages {
  // 私有构造函数
  AppPages._();

  /// 初始路由
  static const String initial = AppRoutes.healthMain;

  /// 所有页面路由配置
  static final routes = [
    // 首页
    GetPage(
      name: AppRoutes.initial,
      page: () => const MyHomePage(title: 'OHOS健康应用'),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 主页面
    GetPage(
      name: AppRoutes.healthMain,
      page: () => const HealthMainView(),
      binding: BindingsBuilder(() {
        Get.lazyPut<HealthMainController>(() => HealthMainController());
      }),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 引导页面
    GetPage(
      name: AppRoutes.guide,
      page: () => const GuideView(),
      binding: BindingsBuilder(() {
        Get.lazyPut<GuideController>(() => GuideController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 引导子页面
    GetPage(
      name: AppRoutes.guideWelcome,
      // page: () => const GuideWelcomeView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        Get.lazyPut<GuideController>(() => GuideController());
      }),
    ),

    GetPage(
      name: AppRoutes.guideDuration,
      // page: () => const GuideDurationView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        Get.lazyPut<GuideController>(() => GuideController());
      }),
    ),

    GetPage(
      name: AppRoutes.guideCycle,
      // page: () => const GuideCycleView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        Get.lazyPut<GuideController>(() => GuideController());
      }),
    ),

    GetPage(
      name: AppRoutes.guideLastPeriod,
      // page: () => const GuideLastPeriodView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        Get.lazyPut<GuideController>(() => GuideController());
      }),
    ),

    GetPage(
      name: AppRoutes.guideNotifications,
      // page: () => const GuideNotificationsView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        Get.lazyPut<GuideController>(() => GuideController());
      }),
    ),

    GetPage(
      name: AppRoutes.guideComplete,
      // page: () => const GuideCompleteView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        Get.lazyPut<GuideController>(() => GuideController());
      }),
    ),

    // 添加记录页面
    GetPage(
      name: AppRoutes.addRecord,
      page: () => AddRecordView(),
      binding: BindingsBuilder(() {
        Get.find<HealthMainController>(); // 复用主页面控制器
      }),
      transition: Transition.downToUp,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 添加症状页面
    GetPage(
      name: AppRoutes.addSymptom,
      // page: () => const AddSymptomView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        // Get.lazyPut<SymptomController>(() => SymptomController());
      }),
      transition: Transition.downToUp,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 预测页面
    GetPage(
      name: AppRoutes.forecast,
      page: () => const ForecastView(),
      binding: BindingsBuilder(() {
        Get.find<HealthMainController>(); // 复用主页面控制器
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 设置页面
    GetPage(
      name: AppRoutes.settings,
      page: () => const SettingsView(),
      binding: BindingsBuilder(() {
        Get.find<HealthMainController>(); // 复用主页面控制器
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 统计页面
    GetPage(
      name: AppRoutes.statistics,
      page: () => const StatisticsView(),
      binding: BindingsBuilder(() {
        Get.find<HealthMainController>(); // 复用主页面控制器
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 日历页面
    GetPage(
      name: AppRoutes.calendar,
      page: () => const CalendarView(),
      binding: BindingsBuilder(() {
        Get.find<HealthMainController>(); // 复用主页面控制器
      }),
      transition: Transition.fadeIn,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 历史记录页面
    GetPage(
      name: AppRoutes.history,
      // page: () => const HistoryView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        // Get.lazyPut<HistoryController>(() => HistoryController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 通知页面
    GetPage(
      name: AppRoutes.notifications,
      // page: () => const NotificationsView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        // Get.lazyPut<NotificationsController>(() => NotificationsController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 个人资料页面
    GetPage(
      name: AppRoutes.profile,
      // page: () => const ProfileView(),
      page: () => Container(), // 临时占位符
      binding: BindingsBuilder(() {
        // Get.lazyPut<ProfileController>(() => ProfileController());
      }),
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 关于页面
    GetPage(
      name: AppRoutes.about,
      // page: () => const AboutView(),
      page: () => Container(), // 临时占位符
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),

    // 帮助页面
    GetPage(
      name: AppRoutes.help,
      // page: () => const HelpView(),
      page: () => Container(), // 临时占位符
      transition: Transition.rightToLeft,
      transitionDuration: const Duration(milliseconds: 300),
    ),
  ];

  /// 获取页面绑定
  static Bindings? getBinding(String routeName) {
    final page = routes.firstWhereOrNull((page) => page.name == routeName);
    return page?.binding;
  }

  /// 获取页面转场动画
  static Transition? getTransition(String routeName) {
    final page = routes.firstWhereOrNull((page) => page.name == routeName);
    return page?.transition;
  }

  /// 检查路由是否需要认证
  static bool requiresAuth(String routeName) {
    // 大部分页面都需要用户数据，但引导页面不需要
    return !AppRoutes.isGuideRoute(routeName) && 
           routeName != AppRoutes.about && 
           routeName != AppRoutes.help;
  }

  /// 检查路由是否需要完成引导
  static bool requiresGuideCompletion(String routeName) {
    // 除了引导页面，其他页面都需要完成引导
    return !AppRoutes.isGuideRoute(routeName) && 
           routeName != AppRoutes.about && 
           routeName != AppRoutes.help;
  }

  /// 获取默认路由（根据用户状态）
  static String getDefaultRoute({
    bool isGuideCompleted = false,
    bool isLoggedIn = true,
  }) {
    if (!isLoggedIn) {
      return AppRoutes.guide; // 如果未登录，跳转到引导页面
    }
    
    if (!isGuideCompleted) {
      return AppRoutes.guide; // 如果未完成引导，跳转到引导页面
    }
    
    return AppRoutes.healthMain; // 默认跳转到主页面
  }
}
