import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/models.dart';

/// 自定义日历组件
class CustomCalendar extends StatefulWidget {
  /// 日历数据
  final List<CalendarSelectData> calendarData;
  
  /// 选中日期回调
  final Function(DateTime)? onDateSelected;
  
  /// 月份改变回调
  final Function(DateTime)? onMonthChanged;
  
  /// 当前选中日期
  final DateTime? selectedDate;
  
  /// 是否显示周视图
  final bool isWeekView;
  
  /// 日历高度
  final double? height;

  const CustomCalendar({
    Key? key,
    required this.calendarData,
    this.onDateSelected,
    this.onMonthChanged,
    this.selectedDate,
    this.isWeekView = false,
    this.height,
  }) : super(key: key);

  @override
  State<CustomCalendar> createState() => _CustomCalendarState();
}

class _CustomCalendarState extends State<CustomCalendar> {
  late DateTime _currentMonth;
  late PageController _pageController;

  @override
  void initState() {
    super.initState();
    _currentMonth = widget.selectedDate ?? DateTime.now();
    _pageController = PageController(initialPage: 1000);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height ?? (widget.isWeekView ? 80 : 320),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: widget.isWeekView ? _buildWeekView() : _buildMonthView(),
          ),
        ],
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            onPressed: _previousMonth,
            icon: const Icon(Icons.chevron_left),
          ),
          Text(
            '${_currentMonth.year}年${_currentMonth.month}月',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          IconButton(
            onPressed: _nextMonth,
            icon: const Icon(Icons.chevron_right),
          ),
        ],
      ),
    );
  }

  /// 构建月视图
  Widget _buildMonthView() {
    return Column(
      children: [
        _buildWeekDayHeader(),
        Expanded(
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: _onPageChanged,
            itemBuilder: (context, index) {
              final month = DateTime(_currentMonth.year, _currentMonth.month + (index - 1000), 1);
              return _buildMonthGrid(month);
            },
          ),
        ),
      ],
    );
  }

  /// 构建周视图
  Widget _buildWeekView() {
    return Column(
      children: [
        _buildWeekDayHeader(),
        Expanded(
          child: _buildWeekGrid(),
        ),
      ],
    );
  }

  /// 构建星期标题
  Widget _buildWeekDayHeader() {
    const weekDays = ['日', '一', '二', '三', '四', '五', '六'];
    return Container(
      height: 40,
      child: Row(
        children: weekDays.map((day) => Expanded(
          child: Center(
            child: Text(
              day,
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        )).toList(),
      ),
    );
  }

  /// 构建月份网格
  Widget _buildMonthGrid(DateTime month) {
    final firstDayOfMonth = DateTime(month.year, month.month, 1);
    final lastDayOfMonth = DateTime(month.year, month.month + 1, 0);
    final firstDayWeekday = firstDayOfMonth.weekday % 7;
    final daysInMonth = lastDayOfMonth.day;
    
    final List<Widget> dayWidgets = [];
    
    // 添加上个月的空白天数
    for (int i = 0; i < firstDayWeekday; i++) {
      dayWidgets.add(Container());
    }
    
    // 添加当月的天数
    for (int day = 1; day <= daysInMonth; day++) {
      final date = DateTime(month.year, month.month, day);
      final calendarData = _getCalendarDataForDate(date);
      dayWidgets.add(_buildDayWidget(date, calendarData));
    }
    
    return GridView.count(
      crossAxisCount: 7,
      children: dayWidgets,
    );
  }

  /// 构建周网格
  Widget _buildWeekGrid() {
    final selectedDate = widget.selectedDate ?? DateTime.now();
    final startOfWeek = selectedDate.subtract(Duration(days: selectedDate.weekday % 7));
    
    final List<Widget> dayWidgets = [];
    
    for (int i = 0; i < 7; i++) {
      final date = startOfWeek.add(Duration(days: i));
      final calendarData = _getCalendarDataForDate(date);
      dayWidgets.add(_buildDayWidget(date, calendarData));
    }
    
    return Row(
      children: dayWidgets.map((widget) => Expanded(child: widget)).toList(),
    );
  }

  /// 构建单个日期组件
  Widget _buildDayWidget(DateTime date, CalendarSelectData? calendarData) {
    final isSelected = widget.selectedDate != null && 
        _isSameDay(date, widget.selectedDate!);
    final isToday = _isSameDay(date, DateTime.now());
    final isCurrentMonth = date.month == _currentMonth.month;
    
    // 确定背景颜色和标记
    Color? backgroundColor;
    Color? borderColor;
    List<Widget> markers = [];
    
    if (calendarData != null) {
      // 月经期标记
      if (calendarData.hasMenstrualRecord && calendarData.isMenstruationDay) {
        backgroundColor = Colors.red[100];
        borderColor = Colors.red;
        markers.add(_buildMarker(Colors.red, size: 6));
      }
      
      // 预测月经期标记
      if (calendarData.isMenstruationForecastDay) {
        backgroundColor = backgroundColor ?? Colors.pink[50];
        borderColor = borderColor ?? Colors.pink[200];
        markers.add(_buildMarker(Colors.pink[300]!, size: 4));
      }
      
      // 排卵期标记
      if (calendarData.isOvulationForecastDay) {
        backgroundColor = backgroundColor ?? Colors.blue[50];
        borderColor = borderColor ?? Colors.blue[200];
        markers.add(_buildMarker(Colors.blue, size: 4));
      }
      
      // 症状标记
      if (calendarData.hasSymptoms) {
        markers.add(_buildMarker(Colors.orange, size: 3));
      }
    }
    
    // 选中状态
    if (isSelected) {
      backgroundColor = Theme.of(context).primaryColor.withOpacity(0.3);
      borderColor = Theme.of(context).primaryColor;
    }
    
    // 今天标记
    if (isToday && !isSelected) {
      borderColor = borderColor ?? Theme.of(context).primaryColor;
    }
    
    return GestureDetector(
      onTap: () => _onDateTap(date),
      child: Container(
        margin: const EdgeInsets.all(2),
        decoration: BoxDecoration(
          color: backgroundColor,
          border: borderColor != null ? Border.all(color: borderColor, width: 1.5) : null,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '${date.day}',
              style: TextStyle(
                color: isCurrentMonth ? Colors.black : Colors.grey,
                fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                fontSize: 16,
              ),
            ),
            if (markers.isNotEmpty) ...[
              const SizedBox(height: 2),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: markers,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建标记点
  Widget _buildMarker(Color color, {double size = 4}) {
    return Container(
      width: size,
      height: size,
      margin: const EdgeInsets.symmetric(horizontal: 1),
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
    );
  }

  /// 获取指定日期的日历数据
  CalendarSelectData? _getCalendarDataForDate(DateTime date) {
    return widget.calendarData.firstWhereOrNull(
      (data) => _isSameDay(data.dateTime, date),
    );
  }

  /// 判断是否是同一天
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// 日期点击事件
  void _onDateTap(DateTime date) {
    widget.onDateSelected?.call(date);
  }

  /// 上一个月
  void _previousMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month - 1, 1);
    });
    widget.onMonthChanged?.call(_currentMonth);
    _pageController.previousPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  /// 下一个月
  void _nextMonth() {
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + 1, 1);
    });
    widget.onMonthChanged?.call(_currentMonth);
    _pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  /// 页面改变事件
  void _onPageChanged(int index) {
    final monthOffset = index - 1000;
    setState(() {
      _currentMonth = DateTime(_currentMonth.year, _currentMonth.month + monthOffset, 1);
    });
    widget.onMonthChanged?.call(_currentMonth);
  }
}
