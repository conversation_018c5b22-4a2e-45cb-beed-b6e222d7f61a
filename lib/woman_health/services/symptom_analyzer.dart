import '../models/menstrual_record.dart';

/// 症状统计结果
class SymptomStatistics {
  /// 症状类型
  final int symptomType;
  
  /// 症状名称
  final String symptomName;
  
  /// 出现次数
  final int occurrenceCount;
  
  /// 出现频率 (0-1)
  final double frequency;
  
  /// 最近出现日期
  final DateTime? lastOccurrence;
  
  /// 平均持续天数
  final double averageDuration;

  SymptomStatistics({
    required this.symptomType,
    required this.symptomName,
    required this.occurrenceCount,
    required this.frequency,
    this.lastOccurrence,
    required this.averageDuration,
  });

  @override
  String toString() {
    return 'SymptomStatistics{symptomName: $symptomName, '
        'occurrenceCount: $occurrenceCount, frequency: ${(frequency * 100).toStringAsFixed(1)}%, '
        'lastOccurrence: $lastOccurrence}';
  }
}

/// 症状趋势分析结果
class SymptomTrend {
  /// 症状类型
  final int symptomType;
  
  /// 症状名称
  final String symptomName;
  
  /// 趋势类型：1-增加，0-稳定，-1-减少
  final int trendType;
  
  /// 趋势强度 (0-1)
  final double trendStrength;
  
  /// 月度数据 Map<月份, 出现次数>
  final Map<String, int> monthlyData;

  SymptomTrend({
    required this.symptomType,
    required this.symptomName,
    required this.trendType,
    required this.trendStrength,
    required this.monthlyData,
  });

  /// 获取趋势描述
  String get trendDescription {
    switch (trendType) {
      case 1:
        return '症状呈上升趋势';
      case -1:
        return '症状呈下降趋势';
      default:
        return '症状保持稳定';
    }
  }

  @override
  String toString() {
    return 'SymptomTrend{symptomName: $symptomName, '
        'trendType: $trendType, trendStrength: ${(trendStrength * 100).toStringAsFixed(1)}%, '
        'description: $trendDescription}';
  }
}

/// 周期阶段症状分析
class CyclePhaseSymptoms {
  /// 月经期症状
  final Map<int, double> menstrualPhaseSymptoms;
  
  /// 卵泡期症状
  final Map<int, double> follicularPhaseSymptoms;
  
  /// 排卵期症状
  final Map<int, double> ovulationPhaseSymptoms;
  
  /// 黄体期症状
  final Map<int, double> lutealPhaseSymptoms;

  CyclePhaseSymptoms({
    required this.menstrualPhaseSymptoms,
    required this.follicularPhaseSymptoms,
    required this.ovulationPhaseSymptoms,
    required this.lutealPhaseSymptoms,
  });

  /// 获取指定阶段的主要症状
  List<String> getMainSymptomsForPhase(String phase) {
    Map<int, double> phaseSymptoms;
    
    switch (phase) {
      case 'menstrual':
        phaseSymptoms = menstrualPhaseSymptoms;
        break;
      case 'follicular':
        phaseSymptoms = follicularPhaseSymptoms;
        break;
      case 'ovulation':
        phaseSymptoms = ovulationPhaseSymptoms;
        break;
      case 'luteal':
        phaseSymptoms = lutealPhaseSymptoms;
        break;
      default:
        return [];
    }
    
    // 按频率排序，返回前3个症状
    final sortedSymptoms = phaseSymptoms.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedSymptoms
        .take(3)
        .map((entry) => MenstrualRecord.getSymptomName(entry.key))
        .toList();
  }
}

/// 症状分析器
class SymptomAnalyzer {
  static const String _tag = 'SymptomAnalyzer';

  /// 分析症状统计
  static List<SymptomStatistics> analyzeSymptomStatistics(List<MenstrualRecord> records) {
    if (records.isEmpty) return [];

    final Map<int, List<MenstrualRecord>> symptomRecords = {};
    
    // 按症状类型分组
    for (final record in records) {
      for (final symptomType in MenstrualRecord.symptomsTypeArray) {
        if (record.hasSymptom(symptomType)) {
          symptomRecords.putIfAbsent(symptomType, () => []);
          symptomRecords[symptomType]!.add(record);
        }
      }
    }
    
    final List<SymptomStatistics> statistics = [];
    final totalRecords = records.length;
    
    for (final symptomType in MenstrualRecord.symptomsTypeArray) {
      final symptomRecordList = symptomRecords[symptomType] ?? [];
      final occurrenceCount = symptomRecordList.length;
      final frequency = totalRecords > 0 ? occurrenceCount / totalRecords : 0.0;
      
      DateTime? lastOccurrence;
      if (symptomRecordList.isNotEmpty) {
        symptomRecordList.sort((a, b) => b.time.compareTo(a.time));
        lastOccurrence = symptomRecordList.first.dateTime;
      }
      
      // 计算平均持续天数（简化计算，实际可能需要更复杂的逻辑）
      final averageDuration = _calculateAverageDuration(symptomRecordList);
      
      final statistic = SymptomStatistics(
        symptomType: symptomType,
        symptomName: MenstrualRecord.getSymptomName(symptomType),
        occurrenceCount: occurrenceCount,
        frequency: frequency,
        lastOccurrence: lastOccurrence,
        averageDuration: averageDuration,
      );
      
      statistics.add(statistic);
    }
    
    // 按出现频率排序
    statistics.sort((a, b) => b.frequency.compareTo(a.frequency));
    
    return statistics;
  }

  /// 分析症状趋势
  static List<SymptomTrend> analyzeSymptomTrends(List<MenstrualRecord> records) {
    if (records.isEmpty) return [];

    // 按月份分组
    final Map<String, List<MenstrualRecord>> monthlyRecords = {};
    for (final record in records) {
      final monthKey = '${record.year}-${record.month.toString().padLeft(2, '0')}';
      monthlyRecords.putIfAbsent(monthKey, () => []);
      monthlyRecords[monthKey]!.add(record);
    }
    
    final List<SymptomTrend> trends = [];
    
    for (final symptomType in MenstrualRecord.symptomsTypeArray) {
      final Map<String, int> monthlyData = {};
      
      // 统计每月该症状的出现次数
      for (final entry in monthlyRecords.entries) {
        final monthKey = entry.key;
        final monthRecords = entry.value;
        
        int symptomCount = 0;
        for (final record in monthRecords) {
          if (record.hasSymptom(symptomType)) {
            symptomCount++;
          }
        }
        
        monthlyData[monthKey] = symptomCount;
      }
      
      // 计算趋势
      final trendResult = _calculateTrend(monthlyData);
      
      final trend = SymptomTrend(
        symptomType: symptomType,
        symptomName: MenstrualRecord.getSymptomName(symptomType),
        trendType: trendResult['type'] as int,
        trendStrength: trendResult['strength'] as double,
        monthlyData: monthlyData,
      );
      
      trends.add(trend);
    }
    
    return trends;
  }

  /// 分析周期阶段症状
  static CyclePhaseSymptoms analyzeCyclePhaseSymptoms(
    List<MenstrualRecord> records,
    int averageCycleLength,
  ) {
    final Map<int, double> menstrualPhase = {};
    final Map<int, double> follicularPhase = {};
    final Map<int, double> ovulationPhase = {};
    final Map<int, double> lutealPhase = {};
    
    // 按周期分组
    final Map<String, List<MenstrualRecord>> cycleRecords = {};
    for (final record in records) {
      final cycleKey = '${record.year}-${record.month}';
      cycleRecords.putIfAbsent(cycleKey, () => []);
      cycleRecords[cycleKey]!.add(record);
    }
    
    for (final cycleRecordList in cycleRecords.values) {
      if (cycleRecordList.isEmpty) continue;
      
      cycleRecordList.sort((a, b) => a.time.compareTo(b.time));
      final cycleStartDate = cycleRecordList.first.dateTime;
      
      for (final record in cycleRecordList) {
        final dayInCycle = record.dateTime.difference(cycleStartDate).inDays + 1;
        final phase = _getCyclePhase(dayInCycle, averageCycleLength);
        
        Map<int, double> phaseMap;
        switch (phase) {
          case 'menstrual':
            phaseMap = menstrualPhase;
            break;
          case 'follicular':
            phaseMap = follicularPhase;
            break;
          case 'ovulation':
            phaseMap = ovulationPhase;
            break;
          case 'luteal':
            phaseMap = lutealPhase;
            break;
          default:
            continue;
        }
        
        // 统计该阶段的症状
        for (final symptomType in MenstrualRecord.symptomsTypeArray) {
          if (record.hasSymptom(symptomType)) {
            phaseMap[symptomType] = (phaseMap[symptomType] ?? 0) + 1;
          }
        }
      }
    }
    
    // 转换为频率
    final totalCycles = cycleRecords.length.toDouble();
    if (totalCycles > 0) {
      _normalizeSymptomFrequency(menstrualPhase, totalCycles);
      _normalizeSymptomFrequency(follicularPhase, totalCycles);
      _normalizeSymptomFrequency(ovulationPhase, totalCycles);
      _normalizeSymptomFrequency(lutealPhase, totalCycles);
    }
    
    return CyclePhaseSymptoms(
      menstrualPhaseSymptoms: menstrualPhase,
      follicularPhaseSymptoms: follicularPhase,
      ovulationPhaseSymptoms: ovulationPhase,
      lutealPhaseSymptoms: lutealPhase,
    );
  }

  /// 获取症状建议
  static List<String> getSymptomAdvice(List<SymptomStatistics> statistics) {
    final List<String> advice = [];
    
    for (final stat in statistics.take(3)) { // 只处理前3个最常见症状
      if (stat.frequency > 0.5) { // 出现频率超过50%
        switch (stat.symptomType) {
          case MenstrualRecord.symptomsTypeAbdominalColic:
            advice.add('腹部绞痛频繁，建议适当运动和热敷缓解');
            break;
          case MenstrualRecord.symptomsTypeHeadache:
            advice.add('头痛症状较多，注意休息和水分补充');
            break;
          case MenstrualRecord.symptomsTypeMoodChanges:
            advice.add('情绪变化明显，建议保持规律作息和适度运动');
            break;
          case MenstrualRecord.symptomsTypeFatigue:
            advice.add('疲劳感较重，注意营养补充和充足睡眠');
            break;
          case MenstrualRecord.symptomsTypeBreastPain:
            advice.add('乳房疼痛常见，可适当按摩和穿合适内衣');
            break;
        }
      }
    }
    
    if (advice.isEmpty) {
      advice.add('症状记录良好，继续保持健康的生活方式');
    }
    
    return advice;
  }

  /// 计算平均持续天数
  static double _calculateAverageDuration(List<MenstrualRecord> records) {
    if (records.isEmpty) return 0.0;
    
    // 简化计算：假设每次记录代表1天
    // 实际应用中可能需要更复杂的逻辑来计算连续天数
    return 1.0;
  }

  /// 计算趋势
  static Map<String, dynamic> _calculateTrend(Map<String, int> monthlyData) {
    if (monthlyData.length < 3) {
      return {'type': 0, 'strength': 0.0};
    }
    
    final sortedEntries = monthlyData.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));
    
    final values = sortedEntries.map((e) => e.value.toDouble()).toList();
    
    // 简单线性回归计算趋势
    final n = values.length;
    final x = List.generate(n, (i) => i.toDouble());
    final sumX = x.reduce((a, b) => a + b);
    final sumY = values.reduce((a, b) => a + b);
    final sumXY = List.generate(n, (i) => x[i] * values[i]).reduce((a, b) => a + b);
    final sumX2 = x.map((xi) => xi * xi).reduce((a, b) => a + b);
    
    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    
    int trendType;
    if (slope > 0.1) {
      trendType = 1; // 上升
    } else if (slope < -0.1) {
      trendType = -1; // 下降
    } else {
      trendType = 0; // 稳定
    }
    
    final strength = (slope.abs() / (values.isNotEmpty ? values.reduce((a, b) => a > b ? a : b) : 1)).clamp(0.0, 1.0);
    
    return {'type': trendType, 'strength': strength};
  }

  /// 获取周期阶段
  static String _getCyclePhase(int dayInCycle, int cycleLength) {
    if (dayInCycle <= 5) {
      return 'menstrual'; // 月经期
    } else if (dayInCycle <= cycleLength ~/ 2 - 2) {
      return 'follicular'; // 卵泡期
    } else if (dayInCycle <= cycleLength ~/ 2 + 2) {
      return 'ovulation'; // 排卵期
    } else {
      return 'luteal'; // 黄体期
    }
  }

  /// 标准化症状频率
  static void _normalizeSymptomFrequency(Map<int, double> symptomMap, double totalCycles) {
    for (final key in symptomMap.keys) {
      symptomMap[key] = symptomMap[key]! / totalCycles;
    }
  }
}
