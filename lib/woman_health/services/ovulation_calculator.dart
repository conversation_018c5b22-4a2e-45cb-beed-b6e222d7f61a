import '../models/record_history_data.dart';
import '../models/menstrual_record.dart';

/// 排卵期计算结果
class OvulationResult {
  /// 排卵日
  final DateTime ovulationDate;
  
  /// 易孕期开始日期
  final DateTime fertileStartDate;
  
  /// 易孕期结束日期
  final DateTime fertileEndDate;
  
  /// 预测准确度 (0-100)
  final int accuracy;
  
  /// 是否是预测数据
  final bool isForecast;

  OvulationResult({
    required this.ovulationDate,
    required this.fertileStartDate,
    required this.fertileEndDate,
    required this.accuracy,
    this.isForecast = true,
  });

  /// 检查指定日期是否在易孕期内
  bool isInFertilePeriod(DateTime date) {
    final checkDate = DateTime(date.year, date.month, date.day);
    final startDate = DateTime(fertileStartDate.year, fertileStartDate.month, fertileStartDate.day);
    final endDate = DateTime(fertileEndDate.year, fertileEndDate.month, fertileEndDate.day);
    
    return checkDate.isAtSameMomentAs(startDate) ||
           checkDate.isAtSameMomentAs(endDate) ||
           (checkDate.isAfter(startDate) && checkDate.isBefore(endDate));
  }

  /// 检查指定日期是否是排卵日
  bool isOvulationDay(DateTime date) {
    final checkDate = DateTime(date.year, date.month, date.day);
    final ovulationDay = DateTime(ovulationDate.year, ovulationDate.month, ovulationDate.day);
    return checkDate.isAtSameMomentAs(ovulationDay);
  }

  @override
  String toString() {
    return 'OvulationResult{ovulationDate: $ovulationDate, '
        'fertileStartDate: $fertileStartDate, fertileEndDate: $fertileEndDate, '
        'accuracy: $accuracy, isForecast: $isForecast}';
  }
}

/// 排卵期计算器
class OvulationCalculator {
  static const String _tag = 'OvulationCalculator';

  /// 计算排卵期
  /// [lastMenstruationDate] 最后一次月经开始日期
  /// [cycleLength] 月经周期长度（天）
  /// [lutealPhaseLength] 黄体期长度（天），默认14天
  static OvulationResult calculateOvulation({
    required DateTime lastMenstruationDate,
    required int cycleLength,
    int lutealPhaseLength = 14,
  }) {
    // 排卵日 = 下次月经开始日期 - 黄体期长度
    final nextMenstruationDate = lastMenstruationDate.add(Duration(days: cycleLength));
    final ovulationDate = nextMenstruationDate.subtract(Duration(days: lutealPhaseLength));
    
    // 易孕期：排卵日前5天到排卵日后1天
    final fertileStartDate = ovulationDate.subtract(const Duration(days: 5));
    final fertileEndDate = ovulationDate.add(const Duration(days: 1));
    
    // 计算准确度（基于周期规律性）
    final accuracy = _calculateAccuracy(cycleLength);
    
    return OvulationResult(
      ovulationDate: ovulationDate,
      fertileStartDate: fertileStartDate,
      fertileEndDate: fertileEndDate,
      accuracy: accuracy,
      isForecast: true,
    );
  }

  /// 基于历史数据计算排卵期
  static List<OvulationResult> calculateOvulationFromHistory({
    required List<MenstrualRecord> menstrualHistory,
    int predictMonths = 6,
  }) {
    if (menstrualHistory.isEmpty) {
      return [];
    }

    // 按时间排序
    menstrualHistory.sort((a, b) => a.time.compareTo(b.time));
    
    // 计算平均周期长度
    final averageCycle = _calculateAverageCycleLength(menstrualHistory);
    
    // 获取最后一次月经记录
    final lastMenstruation = menstrualHistory.last;
    final lastMenstruationDate = lastMenstruation.dateTime;
    
    final List<OvulationResult> results = [];
    
    // 预测未来几个月的排卵期
    for (int i = 1; i <= predictMonths; i++) {
      final cycleStartDate = lastMenstruationDate.add(Duration(days: averageCycle * i));
      
      final ovulationResult = calculateOvulation(
        lastMenstruationDate: cycleStartDate,
        cycleLength: averageCycle,
      );
      
      results.add(ovulationResult);
    }
    
    return results;
  }

  /// 计算基础体温排卵期预测
  /// [temperatureData] 基础体温数据 Map<日期, 体温>
  static DateTime? calculateOvulationFromTemperature(Map<DateTime, double> temperatureData) {
    if (temperatureData.length < 10) {
      return null; // 数据不足
    }

    final sortedEntries = temperatureData.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    // 寻找体温升高的转折点
    for (int i = 3; i < sortedEntries.length - 3; i++) {
      final currentTemp = sortedEntries[i].value;
      final previousTemps = sortedEntries.sublist(i - 3, i).map((e) => e.value).toList();
      final nextTemps = sortedEntries.sublist(i + 1, i + 4).map((e) => e.value).toList();
      
      final avgPrevious = previousTemps.reduce((a, b) => a + b) / previousTemps.length;
      final avgNext = nextTemps.reduce((a, b) => a + b) / nextTemps.length;
      
      // 如果体温有明显升高（通常0.2-0.5度），可能是排卵
      if (avgNext - avgPrevious >= 0.2 && currentTemp < avgNext) {
        return sortedEntries[i].key;
      }
    }
    
    return null;
  }

  /// 计算宫颈粘液排卵期预测
  /// [mucusData] 宫颈粘液数据 Map<日期, 粘液类型>
  /// 粘液类型：1-干燥，2-粘稠，3-乳白色，4-透明拉丝状
  static DateTime? calculateOvulationFromMucus(Map<DateTime, int> mucusData) {
    if (mucusData.isEmpty) {
      return null;
    }

    final sortedEntries = mucusData.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    // 寻找透明拉丝状粘液的最后一天（通常是排卵日）
    DateTime? lastEggWhiteDay;
    
    for (final entry in sortedEntries) {
      if (entry.value == 4) { // 透明拉丝状
        lastEggWhiteDay = entry.key;
      }
    }
    
    return lastEggWhiteDay;
  }

  /// 综合多种方法计算排卵期
  static OvulationResult? calculateComprehensiveOvulation({
    required DateTime lastMenstruationDate,
    required int cycleLength,
    Map<DateTime, double>? temperatureData,
    Map<DateTime, int>? mucusData,
  }) {
    // 基础计算
    final basicResult = calculateOvulation(
      lastMenstruationDate: lastMenstruationDate,
      cycleLength: cycleLength,
    );
    
    DateTime finalOvulationDate = basicResult.ovulationDate;
    int accuracy = basicResult.accuracy;
    
    // 如果有体温数据，调整预测
    if (temperatureData != null) {
      final tempOvulationDate = calculateOvulationFromTemperature(temperatureData);
      if (tempOvulationDate != null) {
        // 如果体温法预测的日期与基础计算相差不超过3天，提高准确度
        final daysDiff = (tempOvulationDate.difference(basicResult.ovulationDate).inDays).abs();
        if (daysDiff <= 3) {
          finalOvulationDate = tempOvulationDate;
          accuracy = (accuracy + 15).clamp(0, 100);
        }
      }
    }
    
    // 如果有宫颈粘液数据，进一步调整
    if (mucusData != null) {
      final mucusOvulationDate = calculateOvulationFromMucus(mucusData);
      if (mucusOvulationDate != null) {
        final daysDiff = (mucusOvulationDate.difference(finalOvulationDate).inDays).abs();
        if (daysDiff <= 2) {
          finalOvulationDate = mucusOvulationDate;
          accuracy = (accuracy + 10).clamp(0, 100);
        }
      }
    }
    
    // 重新计算易孕期
    final fertileStartDate = finalOvulationDate.subtract(const Duration(days: 5));
    final fertileEndDate = finalOvulationDate.add(const Duration(days: 1));
    
    return OvulationResult(
      ovulationDate: finalOvulationDate,
      fertileStartDate: fertileStartDate,
      fertileEndDate: fertileEndDate,
      accuracy: accuracy,
      isForecast: true,
    );
  }

  /// 计算平均周期长度
  static int _calculateAverageCycleLength(List<MenstrualRecord> records) {
    if (records.length < 2) return 28;

    final List<int> cycleLengths = [];
    for (int i = 1; i < records.length; i++) {
      final current = records[i];
      final previous = records[i - 1];
      final daysDiff = current.dateTime.difference(previous.dateTime).inDays;
      if (daysDiff > 0 && daysDiff <= 60) {
        cycleLengths.add(daysDiff);
      }
    }

    if (cycleLengths.isEmpty) return 28;
    
    final sum = cycleLengths.reduce((a, b) => a + b);
    return (sum / cycleLengths.length).round();
  }

  /// 计算预测准确度
  static int _calculateAccuracy(int cycleLength) {
    // 标准28天周期准确度最高
    if (cycleLength >= 26 && cycleLength <= 30) {
      return 85;
    } else if (cycleLength >= 24 && cycleLength <= 32) {
      return 80;
    } else if (cycleLength >= 21 && cycleLength <= 35) {
      return 75;
    } else {
      return 65;
    }
  }
}
