import '../models/menstrual_record.dart';
import '../models/record_history_data.dart';

/// 通知类型
enum NotificationType {
  menstruation,      // 月经提醒
  ovulation,         // 排卵提醒
  fertile,           // 易孕期提醒
  symptomReminder,   // 症状记录提醒
  medicationReminder // 用药提醒
}

/// 通知时间配置
class NotificationConfig {
  /// 通知类型
  final NotificationType type;
  
  /// 是否启用
  final bool enabled;
  
  /// 提前天数
  final int advanceDays;
  
  /// 通知时间（小时）
  final int hour;
  
  /// 通知时间（分钟）
  final int minute;
  
  /// 重复间隔（天）
  final int repeatInterval;
  
  /// 通知标题
  final String title;
  
  /// 通知内容
  final String content;

  NotificationConfig({
    required this.type,
    this.enabled = true,
    this.advanceDays = 1,
    this.hour = 9,
    this.minute = 0,
    this.repeatInterval = 1,
    required this.title,
    required this.content,
  });

  /// 复制配置
  NotificationConfig copyWith({
    NotificationType? type,
    bool? enabled,
    int? advanceDays,
    int? hour,
    int? minute,
    int? repeatInterval,
    String? title,
    String? content,
  }) {
    return NotificationConfig(
      type: type ?? this.type,
      enabled: enabled ?? this.enabled,
      advanceDays: advanceDays ?? this.advanceDays,
      hour: hour ?? this.hour,
      minute: minute ?? this.minute,
      repeatInterval: repeatInterval ?? this.repeatInterval,
      title: title ?? this.title,
      content: content ?? this.content,
    );
  }
}

/// 通知计划
class NotificationSchedule {
  /// 通知ID
  final String id;
  
  /// 通知类型
  final NotificationType type;
  
  /// 计划时间
  final DateTime scheduledTime;
  
  /// 通知标题
  final String title;
  
  /// 通知内容
  final String content;
  
  /// 是否已发送
  final bool isSent;
  
  /// 相关数据
  final Map<String, dynamic>? data;

  NotificationSchedule({
    required this.id,
    required this.type,
    required this.scheduledTime,
    required this.title,
    required this.content,
    this.isSent = false,
    this.data,
  });

  @override
  String toString() {
    return 'NotificationSchedule{id: $id, type: $type, scheduledTime: $scheduledTime, title: $title}';
  }
}

/// 通知时间计算器
class NotificationCalculator {
  static const String _tag = 'NotificationCalculator';

  /// 默认通知配置
  static Map<NotificationType, NotificationConfig> getDefaultConfigs() {
    return {
      NotificationType.menstruation: NotificationConfig(
        type: NotificationType.menstruation,
        enabled: true,
        advanceDays: 1,
        hour: 9,
        minute: 0,
        title: '月经提醒',
        content: '您的月经期即将到来，请做好准备',
      ),
      NotificationType.ovulation: NotificationConfig(
        type: NotificationType.ovulation,
        enabled: true,
        advanceDays: 1,
        hour: 9,
        minute: 0,
        title: '排卵期提醒',
        content: '您即将进入排卵期',
      ),
      NotificationType.fertile: NotificationConfig(
        type: NotificationType.fertile,
        enabled: false,
        advanceDays: 1,
        hour: 9,
        minute: 0,
        title: '易孕期提醒',
        content: '您即将进入易孕期，请注意避孕',
      ),
      NotificationType.symptomReminder: NotificationConfig(
        type: NotificationType.symptomReminder,
        enabled: false,
        advanceDays: 0,
        hour: 20,
        minute: 0,
        repeatInterval: 1,
        title: '症状记录提醒',
        content: '别忘了记录今天的身体状况',
      ),
    };
  }

  /// 计算月经提醒时间
  static List<NotificationSchedule> calculateMenstruationNotifications({
    required List<RecordHistoryData> forecastData,
    required NotificationConfig config,
  }) {
    if (!config.enabled) return [];

    final List<NotificationSchedule> schedules = [];
    
    for (final data in forecastData) {
      if (data.isMenstruationCycle && data.isForecast) {
        final notificationTime = data.startDate
            .subtract(Duration(days: config.advanceDays))
            .add(Duration(hours: config.hour, minutes: config.minute));
        
        // 只安排未来的通知
        if (notificationTime.isAfter(DateTime.now())) {
          final schedule = NotificationSchedule(
            id: 'menstruation_${data.startTime}',
            type: NotificationType.menstruation,
            scheduledTime: notificationTime,
            title: config.title,
            content: '${config.content}，预计${data.formattedStartDate}开始',
            data: {
              'forecastData': data,
              'accuracy': data.accuracy,
            },
          );
          
          schedules.add(schedule);
        }
      }
    }
    
    return schedules;
  }

  /// 计算排卵期提醒时间
  static List<NotificationSchedule> calculateOvulationNotifications({
    required List<RecordHistoryData> forecastData,
    required NotificationConfig config,
  }) {
    if (!config.enabled) return [];

    final List<NotificationSchedule> schedules = [];
    
    for (final data in forecastData) {
      if (data.isOvulationCycle && data.isForecast) {
        final notificationTime = data.startDate
            .subtract(Duration(days: config.advanceDays))
            .add(Duration(hours: config.hour, minutes: config.minute));
        
        if (notificationTime.isAfter(DateTime.now())) {
          final schedule = NotificationSchedule(
            id: 'ovulation_${data.startTime}',
            type: NotificationType.ovulation,
            scheduledTime: notificationTime,
            title: config.title,
            content: '${config.content}，预计${data.formattedStartDate}开始',
            data: {
              'forecastData': data,
              'accuracy': data.accuracy,
            },
          );
          
          schedules.add(schedule);
        }
      }
    }
    
    return schedules;
  }

  /// 计算易孕期提醒时间
  static List<NotificationSchedule> calculateFertileNotifications({
    required List<RecordHistoryData> forecastData,
    required NotificationConfig config,
  }) {
    if (!config.enabled) return [];

    final List<NotificationSchedule> schedules = [];
    
    for (final data in forecastData) {
      if (data.isFertileCycle && data.isForecast) {
        final notificationTime = data.startDate
            .subtract(Duration(days: config.advanceDays))
            .add(Duration(hours: config.hour, minutes: config.minute));
        
        if (notificationTime.isAfter(DateTime.now())) {
          final schedule = NotificationSchedule(
            id: 'fertile_${data.startTime}',
            type: NotificationType.fertile,
            scheduledTime: notificationTime,
            title: config.title,
            content: '${config.content}，预计${data.formattedStartDate}开始',
            data: {
              'forecastData': data,
              'accuracy': data.accuracy,
            },
          );
          
          schedules.add(schedule);
        }
      }
    }
    
    return schedules;
  }

  /// 计算症状记录提醒时间
  static List<NotificationSchedule> calculateSymptomReminderNotifications({
    required NotificationConfig config,
    int futureDays = 30,
  }) {
    if (!config.enabled) return [];

    final List<NotificationSchedule> schedules = [];
    final now = DateTime.now();
    
    for (int i = 0; i < futureDays; i += config.repeatInterval) {
      final reminderDate = now.add(Duration(days: i));
      final notificationTime = DateTime(
        reminderDate.year,
        reminderDate.month,
        reminderDate.day,
        config.hour,
        config.minute,
      );
      
      if (notificationTime.isAfter(now)) {
        final schedule = NotificationSchedule(
          id: 'symptom_reminder_${notificationTime.millisecondsSinceEpoch}',
          type: NotificationType.symptomReminder,
          scheduledTime: notificationTime,
          title: config.title,
          content: config.content,
        );
        
        schedules.add(schedule);
      }
    }
    
    return schedules;
  }

  /// 计算所有通知时间
  static List<NotificationSchedule> calculateAllNotifications({
    required List<RecordHistoryData> menstrualForecast,
    required List<RecordHistoryData> ovulationForecast,
    required Map<NotificationType, NotificationConfig> configs,
  }) {
    final List<NotificationSchedule> allSchedules = [];
    
    // 月经提醒
    if (configs.containsKey(NotificationType.menstruation)) {
      final menstrualSchedules = calculateMenstruationNotifications(
        forecastData: menstrualForecast,
        config: configs[NotificationType.menstruation]!,
      );
      allSchedules.addAll(menstrualSchedules);
    }
    
    // 排卵期提醒
    if (configs.containsKey(NotificationType.ovulation)) {
      final ovulationSchedules = calculateOvulationNotifications(
        forecastData: ovulationForecast,
        config: configs[NotificationType.ovulation]!,
      );
      allSchedules.addAll(ovulationSchedules);
    }
    
    // 易孕期提醒
    if (configs.containsKey(NotificationType.fertile)) {
      final fertileSchedules = calculateFertileNotifications(
        forecastData: ovulationForecast,
        config: configs[NotificationType.fertile]!,
      );
      allSchedules.addAll(fertileSchedules);
    }
    
    // 症状记录提醒
    if (configs.containsKey(NotificationType.symptomReminder)) {
      final symptomSchedules = calculateSymptomReminderNotifications(
        config: configs[NotificationType.symptomReminder]!,
      );
      allSchedules.addAll(symptomSchedules);
    }
    
    // 按时间排序
    allSchedules.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
    
    return allSchedules;
  }

  /// 检查是否需要发送通知
  static List<NotificationSchedule> getNotificationsToSend(
    List<NotificationSchedule> schedules,
  ) {
    final now = DateTime.now();
    return schedules.where((schedule) => 
      !schedule.isSent && 
      schedule.scheduledTime.isBefore(now.add(const Duration(minutes: 5)))
    ).toList();
  }

  /// 计算下次通知时间
  static DateTime? getNextNotificationTime(List<NotificationSchedule> schedules) {
    final now = DateTime.now();
    final futureSchedules = schedules
        .where((schedule) => !schedule.isSent && schedule.scheduledTime.isAfter(now))
        .toList();
    
    if (futureSchedules.isEmpty) return null;
    
    futureSchedules.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
    return futureSchedules.first.scheduledTime;
  }

  /// 获取通知统计信息
  static Map<String, int> getNotificationStatistics(List<NotificationSchedule> schedules) {
    final stats = <String, int>{};
    
    for (final type in NotificationType.values) {
      final count = schedules.where((s) => s.type == type).length;
      stats[type.toString()] = count;
    }
    
    final sentCount = schedules.where((s) => s.isSent).length;
    final pendingCount = schedules.where((s) => !s.isSent).length;
    
    stats['total'] = schedules.length;
    stats['sent'] = sentCount;
    stats['pending'] = pendingCount;
    
    return stats;
  }
}
