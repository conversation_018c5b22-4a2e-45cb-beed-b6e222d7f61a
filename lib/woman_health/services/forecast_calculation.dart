import 'dart:async';
import '../models/record_history_data.dart';
import '../models/menstrual_record.dart';
import 'woman_health_service.dart';

/// 预测计算结果监听器
abstract class ForecastCalculationListener {
  /// 历史周期数据回调
  void onHistoryCycle(List<RecordHistoryData> historyDataList);
  
  /// 月经时间预测回调
  void onMenstruationTime(List<RecordHistoryData> menstrualDataList, int cycle, int duration);
  
  /// 排卵时间预测回调
  void onOvulationTime(List<RecordHistoryData> ovulationDataList, int cycle);
}

/// 月经周期预测计算类
/// 移植自Android版本的ForecastCalculation类
class ForecastCalculation {
  static const String _tag = 'ForecastCalculation';
  
  /// 监听器
  ForecastCalculationListener? _onResultListener;
  
  /// 女性健康服务
  final WomanHealthService _womanHealthService = WomanHealthService();
  
  /// 月经持续时间
  int _duration = 5;
  
  /// 月经周期长度
  int _cycle = 28;
  
  /// 历史记录数据列表
  final List<RecordHistoryData> _recordHistoryDataList = [];
  
  /// 月经预测数据列表
  final List<RecordHistoryData> _recordMenstrualDataList = [];
  
  /// 排卵预测数据列表
  final List<RecordHistoryData> _recordOvulationDataList = [];

  /// 设置结果监听器
  void setOnResultListener(ForecastCalculationListener? listener) {
    _onResultListener = listener;
  }

  /// 异步开始计算
  Future<void> onStartCalculation(int duration, int cycle) async {
    _duration = duration;
    _cycle = cycle;
    _recordMenstrualDataList.clear();
    _recordOvulationDataList.clear();
    
    try {
      await _getHistoryCycle(cycle, duration);
      
      for (int i = 0; i < _recordHistoryDataList.length; i++) {
        final recordHistoryData = _recordHistoryDataList[i];
        _getMenstruationTime(recordHistoryData);
        _getOvulationTime(recordHistoryData);
      }
      
      // 通知监听器
      _onResultListener?.onHistoryCycle(_recordHistoryDataList);
      _onResultListener?.onMenstruationTime(_recordMenstrualDataList, _cycle, _duration);
      _onResultListener?.onOvulationTime(_recordOvulationDataList, _cycle);
    } catch (e) {
      print('$_tag: 预测计算失败: $e');
    }
  }

  /// 同步计算开始计算
  void onStartSyncCalculation(int duration, int cycle) {
    _duration = duration;
    _cycle = cycle;
    _recordMenstrualDataList.clear();
    _recordOvulationDataList.clear();
    
    // 这里使用同步方式，但实际上数据库操作是异步的
    // 在实际使用中建议使用异步版本
    onStartCalculation(duration, cycle);
  }

  /// 获取历史周期数据
  Future<void> _getHistoryCycle(int cycle, int duration) async {
    _recordHistoryDataList.clear();
    
    try {
      // 获取用户ID（这里需要从全局状态或参数中获取）
      const String uid = 'current_user'; // 实际使用时需要替换为真实的用户ID
      
      // 获取历史月经数据
      final historyData = await _womanHealthService.getMenstrualHistoryData(uid);
      _recordHistoryDataList.addAll(historyData);
      
      // 如果历史数据不足，生成预测数据
      if (_recordHistoryDataList.length < 6) {
        _generateForecastData(cycle, duration);
      }
    } catch (e) {
      print('$_tag: 获取历史周期数据失败: $e');
      // 如果获取历史数据失败，生成默认预测数据
      _generateForecastData(cycle, duration);
    }
  }

  /// 生成预测数据
  void _generateForecastData(int cycle, int duration) {
    final now = DateTime.now();
    
    // 生成未来6个月的预测数据
    for (int i = 0; i < 6; i++) {
      final startDate = now.add(Duration(days: cycle * i));
      final endDate = startDate.add(Duration(days: duration - 1));
      
      final forecastData = RecordHistoryData(
        startTime: startDate.millisecondsSinceEpoch,
        endTime: endDate.millisecondsSinceEpoch,
        cycleType: RecordHistoryData.cycleTypeMenstruation,
        cycleDays: cycle,
        durationDays: duration,
        isForecast: true,
        accuracy: 80, // 默认准确度
      );
      
      _recordHistoryDataList.add(forecastData);
    }
  }

  /// 计算月经时间预测
  void _getMenstruationTime(RecordHistoryData recordHistoryData) {
    if (recordHistoryData.isMenstruationCycle) {
      // 生成未来的月经预测
      final baseDate = recordHistoryData.endDate;
      
      for (int i = 1; i <= 6; i++) {
        final nextStartDate = baseDate.add(Duration(days: _cycle * i));
        final nextEndDate = nextStartDate.add(Duration(days: _duration - 1));
        
        final forecastData = RecordHistoryData(
          startTime: nextStartDate.millisecondsSinceEpoch,
          endTime: nextEndDate.millisecondsSinceEpoch,
          cycleType: RecordHistoryData.cycleTypeMenstruation,
          cycleDays: _cycle,
          durationDays: _duration,
          isForecast: true,
          accuracy: _calculateAccuracy(i),
        );
        
        _recordMenstrualDataList.add(forecastData);
      }
    }
  }

  /// 计算排卵时间预测
  void _getOvulationTime(RecordHistoryData recordHistoryData) {
    if (recordHistoryData.isMenstruationCycle) {
      // 排卵期通常在月经开始前14天左右
      final menstruationStartDate = recordHistoryData.startDate;
      
      for (int i = 1; i <= 6; i++) {
        final nextMenstruationDate = menstruationStartDate.add(Duration(days: _cycle * i));
        final ovulationDate = nextMenstruationDate.subtract(const Duration(days: 14));
        
        // 排卵期持续约1-2天，易孕期约5-6天
        final ovulationStartDate = ovulationDate.subtract(const Duration(days: 1));
        final ovulationEndDate = ovulationDate.add(const Duration(days: 1));
        
        final ovulationForecastData = RecordHistoryData(
          startTime: ovulationStartDate.millisecondsSinceEpoch,
          endTime: ovulationEndDate.millisecondsSinceEpoch,
          cycleType: RecordHistoryData.cycleTypeOvulation,
          cycleDays: _cycle,
          durationDays: 2,
          isForecast: true,
          accuracy: _calculateAccuracy(i),
        );
        
        _recordOvulationDataList.add(ovulationForecastData);
        
        // 易孕期（排卵期前后各2-3天）
        final fertileStartDate = ovulationDate.subtract(const Duration(days: 3));
        final fertileEndDate = ovulationDate.add(const Duration(days: 3));
        
        final fertileForecastData = RecordHistoryData(
          startTime: fertileStartDate.millisecondsSinceEpoch,
          endTime: fertileEndDate.millisecondsSinceEpoch,
          cycleType: RecordHistoryData.cycleTypeFertile,
          cycleDays: _cycle,
          durationDays: 6,
          isForecast: true,
          accuracy: _calculateAccuracy(i),
        );
        
        _recordOvulationDataList.add(fertileForecastData);
      }
    }
  }

  /// 计算预测准确度
  int _calculateAccuracy(int monthsAhead) {
    // 预测时间越远，准确度越低
    switch (monthsAhead) {
      case 1:
        return 90;
      case 2:
        return 85;
      case 3:
        return 80;
      case 4:
        return 75;
      case 5:
        return 70;
      case 6:
        return 65;
      default:
        return 60;
    }
  }

  /// 检查指定日期是否在月经期内
  bool isInMenstruationPeriod(DateTime date) {
    for (final data in _recordMenstrualDataList) {
      if (data.containsDate(date)) {
        return true;
      }
    }
    return false;
  }

  /// 检查指定日期是否在排卵期内
  bool isInOvulationPeriod(DateTime date) {
    for (final data in _recordOvulationDataList) {
      if (data.isOvulationCycle && data.containsDate(date)) {
        return true;
      }
    }
    return false;
  }

  /// 检查指定日期是否在易孕期内
  bool isInFertilePeriod(DateTime date) {
    for (final data in _recordOvulationDataList) {
      if (data.isFertileCycle && data.containsDate(date)) {
        return true;
      }
    }
    return false;
  }

  /// 获取下次月经预测日期
  DateTime? getNextMenstruationDate() {
    if (_recordMenstrualDataList.isEmpty) return null;
    
    final now = DateTime.now();
    for (final data in _recordMenstrualDataList) {
      if (data.startDate.isAfter(now)) {
        return data.startDate;
      }
    }
    return null;
  }

  /// 获取下次排卵预测日期
  DateTime? getNextOvulationDate() {
    if (_recordOvulationDataList.isEmpty) return null;
    
    final now = DateTime.now();
    for (final data in _recordOvulationDataList) {
      if (data.isOvulationCycle && data.startDate.isAfter(now)) {
        return data.startDate;
      }
    }
    return null;
  }

  /// 清理资源
  void dispose() {
    _onResultListener = null;
    _recordHistoryDataList.clear();
    _recordMenstrualDataList.clear();
    _recordOvulationDataList.clear();
  }
}
