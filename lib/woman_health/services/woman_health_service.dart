import '../database/gsfit_database.dart';
import '../database/dao/menstrual_record_dao.dart';
import '../models/menstrual_record.dart';
import '../models/record_history_data.dart';

/// 女性健康服务类
/// 提供女性健康相关的数据库操作和业务逻辑
class WomanHealthService {
  static final WomanHealthService _instance = WomanHealthService._internal();
  
  factory WomanHealthService() {
    return _instance;
  }
  
  WomanHealthService._internal();

  /// 获取月经记录DAO
  MenstrualRecordDao get _menstrualRecordDao => 
      GsFitDatabase.healthDatabase.menstrualRecordDao;

  /// 保存月经记录
  Future<int> saveMenstrualRecord(MenstrualRecord record) async {
    try {
      if (record.id == null) {
        // 新增记录
        return await _menstrualRecordDao.insert(record);
      } else {
        // 更新记录
        await _menstrualRecordDao.update(record);
        return record.id!;
      }
    } catch (e) {
      print('保存月经记录失败: $e');
      rethrow;
    }
  }

  /// 批量保存月经记录
  Future<void> saveMenstrualRecords(List<MenstrualRecord> records) async {
    try {
      await _menstrualRecordDao.insertBatch(records);
    } catch (e) {
      print('批量保存月经记录失败: $e');
      rethrow;
    }
  }

  /// 删除月经记录
  Future<bool> deleteMenstrualRecord(int id) async {
    try {
      final result = await _menstrualRecordDao.delete(id);
      return result > 0;
    } catch (e) {
      print('删除月经记录失败: $e');
      return false;
    }
  }

  /// 根据用户ID获取所有月经记录
  Future<List<MenstrualRecord>> getMenstrualRecords(String uid) async {
    try {
      return await _menstrualRecordDao.findByUid(uid);
    } catch (e) {
      print('获取月经记录失败: $e');
      return [];
    }
  }

  /// 根据日期获取月经记录
  Future<MenstrualRecord?> getMenstrualRecordByDate(
    String uid,
    DateTime date,
  ) async {
    try {
      return await _menstrualRecordDao.findByDate(
        uid,
        date.year,
        date.month,
        date.day,
      );
    } catch (e) {
      print('根据日期获取月经记录失败: $e');
      return null;
    }
  }

  /// 根据时间范围获取月经记录
  Future<List<MenstrualRecord>> getMenstrualRecordsByTimeRange(
    String uid,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _menstrualRecordDao.findByTimeRange(
        uid,
        startDate.millisecondsSinceEpoch,
        endDate.millisecondsSinceEpoch,
      );
    } catch (e) {
      print('根据时间范围获取月经记录失败: $e');
      return [];
    }
  }

  /// 获取月经记录（仅包含有出血的记录）
  Future<List<MenstrualRecord>> getMenstruationRecords(String uid) async {
    try {
      return await _menstrualRecordDao.findMenstruationRecords(uid);
    } catch (e) {
      print('获取月经记录失败: $e');
      return [];
    }
  }

  /// 获取最近的月经记录
  Future<MenstrualRecord?> getLatestMenstruationRecord(String uid) async {
    try {
      return await _menstrualRecordDao.findLatestMenstruationRecord(uid);
    } catch (e) {
      print('获取最近月经记录失败: $e');
      return null;
    }
  }

  /// 获取指定月份的月经记录
  Future<List<MenstrualRecord>> getMenstrualRecordsByMonth(
    String uid,
    int year,
    int month,
  ) async {
    try {
      return await _menstrualRecordDao.findByMonth(uid, year, month);
    } catch (e) {
      print('获取月份月经记录失败: $e');
      return [];
    }
  }

  /// 获取有症状的记录
  Future<List<MenstrualRecord>> getRecordsWithSymptoms(String uid) async {
    try {
      return await _menstrualRecordDao.findRecordsWithSymptoms(uid);
    } catch (e) {
      print('获取症状记录失败: $e');
      return [];
    }
  }

  /// 统计月经记录数量
  Future<int> countMenstrualRecords(String uid) async {
    try {
      return await _menstrualRecordDao.countByUid(uid);
    } catch (e) {
      print('统计月经记录数量失败: $e');
      return 0;
    }
  }

  /// 统计月经天数
  Future<int> countMenstruationDays(String uid) async {
    try {
      return await _menstrualRecordDao.countMenstruationDays(uid);
    } catch (e) {
      print('统计月经天数失败: $e');
      return 0;
    }
  }

  /// 计算平均月经周期长度
  Future<int> calculateAverageCycleLength(String uid) async {
    try {
      final records = await getMenstruationRecords(uid);
      if (records.length < 2) return 28; // 默认28天

      final List<int> cycleLengths = [];
      for (int i = 1; i < records.length; i++) {
        final current = records[i];
        final previous = records[i - 1];
        final daysDiff = current.dateTime.difference(previous.dateTime).inDays;
        if (daysDiff > 0 && daysDiff <= 60) { // 合理的周期范围
          cycleLengths.add(daysDiff);
        }
      }

      if (cycleLengths.isEmpty) return 28;
      
      final sum = cycleLengths.reduce((a, b) => a + b);
      return (sum / cycleLengths.length).round();
    } catch (e) {
      print('计算平均周期长度失败: $e');
      return 28;
    }
  }

  /// 计算平均月经持续时间
  Future<int> calculateAverageMenstruationDuration(String uid) async {
    try {
      final records = await getMenstruationRecords(uid);
      if (records.isEmpty) return 5; // 默认5天

      // 按时间分组，计算每次月经的持续时间
      final Map<String, List<MenstrualRecord>> groupedRecords = {};
      
      for (final record in records) {
        final monthKey = '${record.year}-${record.month}';
        groupedRecords.putIfAbsent(monthKey, () => []);
        groupedRecords[monthKey]!.add(record);
      }

      final List<int> durations = [];
      for (final monthRecords in groupedRecords.values) {
        if (monthRecords.isNotEmpty) {
          monthRecords.sort((a, b) => a.time.compareTo(b.time));
          
          int consecutiveDays = 1;
          for (int i = 1; i < monthRecords.length; i++) {
            final current = monthRecords[i];
            final previous = monthRecords[i - 1];
            final daysDiff = current.dateTime.difference(previous.dateTime).inDays;
            
            if (daysDiff == 1) {
              consecutiveDays++;
            } else {
              durations.add(consecutiveDays);
              consecutiveDays = 1;
            }
          }
          durations.add(consecutiveDays);
        }
      }

      if (durations.isEmpty) return 5;
      
      final sum = durations.reduce((a, b) => a + b);
      return (sum / durations.length).round();
    } catch (e) {
      print('计算平均月经持续时间失败: $e');
      return 5;
    }
  }

  /// 检查指定日期是否有月经记录
  Future<bool> hasMenstrualRecordOnDate(String uid, DateTime date) async {
    try {
      final record = await getMenstrualRecordByDate(uid, date);
      return record != null;
    } catch (e) {
      print('检查日期月经记录失败: $e');
      return false;
    }
  }

  /// 获取月经历史数据用于预测
  Future<List<RecordHistoryData>> getMenstrualHistoryData(String uid) async {
    try {
      final records = await getMenstruationRecords(uid);
      if (records.isEmpty) return [];

      final List<RecordHistoryData> historyData = [];
      
      // 按月分组
      final Map<String, List<MenstrualRecord>> groupedRecords = {};
      for (final record in records) {
        final monthKey = '${record.year}-${record.month}';
        groupedRecords.putIfAbsent(monthKey, () => []);
        groupedRecords[monthKey]!.add(record);
      }

      // 为每个月创建历史数据
      for (final monthRecords in groupedRecords.values) {
        if (monthRecords.isNotEmpty) {
          monthRecords.sort((a, b) => a.time.compareTo(b.time));
          
          final startRecord = monthRecords.first;
          final endRecord = monthRecords.last;
          
          final historyItem = RecordHistoryData(
            startTime: startRecord.time,
            endTime: endRecord.time,
            cycleType: RecordHistoryData.cycleTypeMenstruation,
            cycleDays: 28, // 默认值，后续会根据实际数据计算
            durationDays: monthRecords.length,
            isForecast: false,
          );
          
          historyData.add(historyItem);
        }
      }

      // 计算周期长度
      historyData.sort((a, b) => a.startTime.compareTo(b.startTime));
      for (int i = 1; i < historyData.length; i++) {
        final current = historyData[i];
        final previous = historyData[i - 1];
        final cycleDays = current.startDate.difference(previous.startDate).inDays;
        if (cycleDays > 0 && cycleDays <= 60) {
          historyData[i] = current.copyWith(cycleDays: cycleDays);
        }
      }

      return historyData;
    } catch (e) {
      print('获取月经历史数据失败: $e');
      return [];
    }
  }
}
