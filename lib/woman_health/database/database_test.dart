import 'dart:typed_data';
import 'package:flutter_test/flutter_test.dart';
import 'package:ohos_app/woman_health/database/gsfit_database.dart';

/// 数据库测试类
class DatabaseTest {
  static Future<void> runAllTests() async {
    print('开始运行数据库测试...');

    try {
      // 初始化数据库
      await GsFitDatabase.initialize();
      print('✓ 数据库初始化成功');

      // 测试步数数据
      await _testStepData();

      // 测试心率数据
      await _testHeartRateData();

      // 测试查询功能
      await _testQueryFunctions();

      print('✓ 所有数据库测试通过');
    } catch (e) {
      print('✗ 数据库测试失败: $e');
      rethrow;
    } finally {
      // 清理测试数据
      await _cleanupTestData();
    }
  }

  /// 测试步数数据
  static Future<void> _testStepData() async {
    print('测试步数数据...');

    final healthDao = GsFitDatabase.healthDatabase.healthDao;

    // 创建测试步数数据
    final testTimestamp = DateTime.now().millisecondsSinceEpoch;
    const testSteps = 1000;
    final testData = Uint8List.fromList([
      0x00, 0x00, 0x00, 0x00, // 时间戳占位符
      testSteps, // 步数
      0x00, 0x00, 0x00, 0x00, 0x00, // 其他数据
    ]);

    // 创建健康数据实体
    final healthEntity = HealthEntity(
      uid: 'test_user',
      type: HealthDataType.step,
      version: 1,
      time: testTimestamp,
      crcCode: null,
      space: 1,
      sync: false,
      data: testData,
    );

    // 插入数据
    await healthDao.insert(healthEntity);
    print('✓ 步数数据插入成功');

    // 查询数据
    final records = await healthDao.findHealthByDate(
      HealthDataType.step,
      'test_user',
      testTimestamp,
      testTimestamp + 1,
    );

    if (records.isNotEmpty) {
      print('✓ 步数数据查询成功，找到 ${records.length} 条记录');

      final record = records.first;
      print(
          '  记录详情: ID=${record.id}, 时间=${record.time}, 类型=${record.typeName}');
    } else {
      throw Exception('未找到插入的步数数据');
    }
  }

  /// 测试心率数据
  static Future<void> _testHeartRateData() async {
    print('测试心率数据...');

    final healthDao = GsFitDatabase.healthDatabase.healthDao;

    // 创建测试心率数据
    final testTimestamp = DateTime.now().millisecondsSinceEpoch;
    const testHeartRate = 75;
    final testData = Uint8List.fromList([
      0x00, 0x00, 0x00, 0x00, // 时间戳占位符
      testHeartRate, // 心率
      0x00, 0x00, 0x00, 0x00, 0x00, // 其他数据
    ]);

    // 创建健康数据实体
    final healthEntity = HealthEntity(
      uid: 'test_user',
      type: HealthDataType.heartRate,
      version: 1,
      time: testTimestamp,
      crcCode: null,
      space: 1,
      sync: false,
      data: testData,
    );

    // 插入数据
    await healthDao.insert(healthEntity);
    print('✓ 心率数据插入成功');

    // 查询数据
    final records = await healthDao.findHealthByDate(
      HealthDataType.heartRate,
      'test_user',
      testTimestamp,
      testTimestamp + 1,
    );

    if (records.isNotEmpty) {
      print('✓ 心率数据查询成功，找到 ${records.length} 条记录');

      final record = records.first;
      print(
          '  记录详情: ID=${record.id}, 时间=${record.time}, 类型=${record.typeName}');
    } else {
      throw Exception('未找到插入的心率数据');
    }
  }

  /// 测试查询功能
  static Future<void> _testQueryFunctions() async {
    print('测试查询功能...');

    final healthDao = GsFitDatabase.healthDatabase.healthDao;

    // 测试获取最新数据
    final latestStep = await healthDao.getHealthLiveDataLast(
      HealthDataType.step,
      'test_user',
    );

    if (latestStep != null) {
      print('✓ 获取最新步数数据成功: ID=${latestStep.id}');
    } else {
      print('⚠ 未找到最新步数数据');
    }

    // 测试统计每日数据
    final startTime =
        DateTime.now().subtract(Duration(days: 1)).millisecondsSinceEpoch;
    final endTime = DateTime.now().millisecondsSinceEpoch;

    final dailyData = await healthDao.countDailyHealthData(
      'test_user',
      HealthDataType.step,
      startTime ~/ 1000,
      endTime ~/ 1000,
    );

    print('✓ 每日数据统计成功，找到 ${dailyData.length} 条记录');
  }

  /// 清理测试数据
  static Future<void> _cleanupTestData() async {
    print('清理测试数据...');

    try {
      final healthDao = GsFitDatabase.healthDatabase.healthDao;

      // 删除测试用户的所有数据
      // 注意：这里只是示例，实际项目中应该有更精确的清理方法

      print('✓ 测试数据清理完成');
    } catch (e) {
      print('⚠ 清理测试数据时出错: $e');
    }
  }

  /// 测试步数数据解析
  static void testStepDataParsing() {
    print('测试步数数据解析...');

    // 模拟原始步数数据
    final rawData = Uint8List.fromList([
      0x0B, // 命令长度
      0x04, // 数据类型（步数）
      0x00, 0x00, 0x00, 0x00, // 时间戳
      0x01, // 版本
      0xE8, 0x03, // 步数 1000
      0x00, 0x00, // 距离
      0x00, 0x00, // 卡路里
    ]);

    // 解析数据
    final healthEntity = HealthEntity.fromBytes(rawData);

    if (healthEntity != null) {
      print('✓ 步数数据解析成功');
      print('  类型: ${healthEntity.typeName}');
      print('  时间: ${healthEntity.time}');
      print('  版本: ${healthEntity.version}');
    } else {
      print('✗ 步数数据解析失败');
    }
  }

  /// 测试时间计算
  static void testTimeCalculation() {
    print('测试时间计算...');

    // 测试日数据时间计算
    final testDate = DateTime(2025, 6, 24);
    final startOfDay = DateTime(testDate.year, testDate.month, testDate.day);
    final endOfDay = startOfDay.add(Duration(days: 1));

    print('测试日期: ${testDate.toString()}');
    print('开始时间: ${startOfDay.toString()}');
    print('结束时间: ${endOfDay.toString()}');
    print('开始时间戳: ${startOfDay.millisecondsSinceEpoch}');
    print('结束时间戳: ${endOfDay.millisecondsSinceEpoch}');

    // 验证开始时间是否为00:00:00
    if (startOfDay.hour == 0 &&
        startOfDay.minute == 0 &&
        startOfDay.second == 0) {
      print('✓ 日数据开始时间计算正确 (00:00:00)');
    } else {
      print('✗ 日数据开始时间计算错误');
    }

    // 验证结束时间是否为下一天的00:00:00
    if (endOfDay.hour == 0 && endOfDay.minute == 0 && endOfDay.second == 0) {
      print('✓ 日数据结束时间计算正确 (下一天 00:00:00)');
    } else {
      print('✗ 日数据结束时间计算错误');
    }

    // 测试周数据时间计算
    final startOfWeek = testDate.subtract(Duration(days: testDate.weekday - 1));
    final endOfWeek = startOfWeek.add(Duration(days: 6));

    print('周开始时间: ${startOfWeek.toString()}');
    print('周结束时间: ${endOfWeek.toString()}');

    // 测试月数据时间计算
    final startOfMonth = DateTime(testDate.year, testDate.month, 1);
    final endOfMonth = DateTime(testDate.year, testDate.month + 1, 0);

    print('月开始时间: ${startOfMonth.toString()}');
    print('月结束时间: ${endOfMonth.toString()}');

    // 测试年数据时间计算
    final startOfYear = DateTime(testDate.year, 1, 1);
    final endOfYear = DateTime(testDate.year, 12, 31);

    print('年开始时间: ${startOfYear.toString()}');
    print('年结束时间: ${endOfYear.toString()}');
  }
}

/// 简单的测试运行器
void main() async {
  // 测试时间计算
  DatabaseTest.testTimeCalculation();

  // 运行所有测试
  await DatabaseTest.runAllTests();

  // 测试数据解析
  DatabaseTest.testStepDataParsing();
}
