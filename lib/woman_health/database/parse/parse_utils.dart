/// 解析工具类 - 提供字节转换等辅助方法
class ParseUtils {
  /// 将两个字节转换为整数 小端序
  // static int bytesToInt(int byte1, int byte2) {
  //   return (byte1 & 0xFF) | ((byte2 & 0xFF) << 8);
  // }
  /// 将两个字节转换为整数（大端序，与Android版本逻辑一致）
  static int bytesToInt(int byte1, int byte2) {
    return ((byte1 & 0xFF) << 8) | (byte2 & 0xFF);
  }

  /// 将字节数组转换为十六进制字符串
  static String bytesToHexString(List<int> bytes) {
    return bytes.map((byte) => byte.toRadixString(16).padLeft(2, '0')).join('');
  }

  /// 将字节转换为无符号整数
  static int byteToUnsignedInt(int byte) {
    return byte & 0xFF;
  }

  /// 将字节转换为整数
  static int byteToInt(int byte) {
    return byte & 0xFF;
  }

  /// 检查时间是否有效
  static bool isValidTime(int hour, int minute) {
    return hour <= 24 && minute <= 60;
  }

  /// 获取当前时间戳
  static int getCurrentTimestamp() {
    return DateTime.now().millisecondsSinceEpoch;
  }

  /// 创建日历实例并设置时间
  static DateTime createDateTime(
      int year, int month, int day, int hour, int minute) {
    return DateTime(year, month, day, hour, minute);
  }

  /// 获取时间戳
  static int getTimestamp(DateTime dateTime) {
    return dateTime.millisecondsSinceEpoch;
  }

  /// 添加分钟到时间
  static DateTime addMinutes(DateTime dateTime, int minutes) {
    return dateTime.add(Duration(minutes: minutes));
  }

  /// 检查是否在夜间睡眠时间范围内
  static bool isInDarkSleepRange(
      DateTime startDate, DateTime endDate, int durationMinutes) {
    // 设置夜间睡眠时间范围：0点到6点
    final rangeStart =
        DateTime(startDate.year, startDate.month, startDate.day, 0, 0);
    final rangeEnd =
        DateTime(startDate.year, startDate.month, startDate.day, 6, 0);

    // 睡眠时间跨度超过了0点到6点
    bool isContain =
        startDate.isBefore(rangeStart) && endDate.isAfter(rangeEnd);

    // 起始或结束时间在零点到六点且持续时间大于两小时
    bool isInRange = durationMinutes >= 120 &&
        ((startDate.isAfter(rangeStart) && startDate.isBefore(rangeEnd)) ||
            (endDate.isAfter(rangeStart) && endDate.isBefore(rangeEnd)));

    return isContain || isInRange;
  }
}
