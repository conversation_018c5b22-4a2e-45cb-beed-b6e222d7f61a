import 'parse_entity.dart';

/// 多属性解析实体类 - 用于包含多个属性的数据（如步数、距离、卡路里）
class ParseEntityObject extends ParseEntity {
  double attr1;
  double attr2;
  double attr3;

  /// 构造函数
  ParseEntityObject(int startTime, this.attr1, this.attr2, this.attr3)
      : super.withoutEndTime(startTime, attr1);

  @override
  String toString() {
    return 'ParseEntityObject{startTime: $startTime, attr1: $attr1, attr2: $attr2, attr3: $attr3}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ParseEntityObject &&
        super == other &&
        other.attr1 == attr1 &&
        other.attr2 == attr2 &&
        other.attr3 == attr3;
  }

  @override
  int get hashCode {
    return super.hashCode ^ attr1.hashCode ^ attr2.hashCode ^ attr3.hashCode;
  }
}
