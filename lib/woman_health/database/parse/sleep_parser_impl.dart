import '../models/health_entity.dart';
import 'i_parser.dart';
import 'parse_entity.dart';
import 'parse_utils.dart';

/// 睡眠解析器实现
class SleepParserImpl implements IParser<ParseEntity> {
  static const int DATA_TYPE_SLEEP = 0x05;
  SleepAnalysis? analysis;

  @override
  List<ParseEntity> parse(HealthEntity entity) {
    analysis = SleepAnalysis();
    if (entity.version == 0) {
      return ParserV0().parse(entity);
    }
    return [];
  }
}

/// 睡眠分析数据
class SleepAnalysis {
  int analysisSleepGrade = 0; // 睡眠得分
  int analysisDeepSleepRatio = 0; // 深睡比例
  int analysisLightSleepRatio = 0; // 浅睡比例
  int analysisREMRatio = 0; // rem比例
  int analysisAllDurationAppraise = 0; // 总时长评价
  int analysisDeepSleepDurationAppraise = 0; // 深睡评价
  int analysisLightSleepDurationAppraise = 0; // 浅睡评价
  int analysisREMDurationAppraise = 0; // rem评价
  int analysisDeepSleepGrade = 0; // 深睡连续性得分
  int analysisAwakeTime = 0; // 夜间醒来次数
}

/// V0版本睡眠解析器
class ParserV0 implements IParser<ParseEntity> {
  // 睡眠类型映射
  static const Map<int, int> typeValueMap = {
    -1: 3, // 未知
    1: 1, // 深睡
    2: 0, // 浅睡
    3: 2, // REM
    4: 4, // 清醒
  };

  @override
  List<ParseEntity> parse(HealthEntity entity) {
    List<ParseEntity> entities = [];
    List<int>? origin = entity.data;

    if (origin != null && origin.length > 15) {
      if (origin[0] == SleepParserImpl.DATA_TYPE_SLEEP) {
        DateTime calendar = DateTime.now();
        int year = ParseUtils.bytesToInt(origin[1], origin[2]);
        int month = ParseUtils.byteToUnsignedInt(origin[3]);
        int day = ParseUtils.byteToUnsignedInt(origin[4]);
        int offset = 11;

        while (offset + 4 <= origin.length) {
          List<int> tempArray = origin.sublist(offset, offset + 4);
          int hour = ParseUtils.byteToUnsignedInt(tempArray[0]);
          int minute = ParseUtils.byteToUnsignedInt(tempArray[1]);
          int dataLen = ParseUtils.bytesToInt(tempArray[2], tempArray[3]);

          if (ParseUtils.isValidTime(hour, minute)) {
            if (offset + dataLen > origin.length) {
              break;
            }

            calendar =
                ParseUtils.createDateTime(year, month, day, hour, minute);
            List<int> data = origin.sublist(offset + 4, offset + 4 + dataLen);

            // 先计算总时长
            int duration = 0;
            for (int i = 0; i + 2 <= data.length; i += 2) {
              int min = ParseUtils.byteToUnsignedInt(data[i + 1]);
              duration += min;
            }

            DateTime endDateTime = ParseUtils.addMinutes(calendar, duration);
            DateTime startDateTime = calendar;
            int time = ParseUtils.getTimestamp(calendar);

            if (ParseUtils.isInDarkSleepRange(
                startDateTime, endDateTime, duration)) {
              // 夜间睡眠
              ParseEntity? last;
              for (int i = 0; i + 2 <= data.length; i += 2) {
                int? type = typeValueMap[ParseUtils.byteToUnsignedInt(data[i])];
                if (type == null) continue;

                int min = ParseUtils.byteToUnsignedInt(data[i + 1]);
                min = min > 0 ? min : 0;
                int endTime = time + min * 60000;

                if (last != null && last.value.toInt() == type) {
                  last.endTime = endTime;
                } else {
                  ParseEntity current =
                      ParseEntity(time, endTime, type.toDouble());
                  entities.add(current);
                  last = current;
                }
                time = endTime;
              }
            } else {
              // 零星小睡
              entities.add(ParseEntity(time, time + duration * 60000, 0x04));
            }
          } else if (tempArray[0] == 0xff && tempArray[1] == 0xff) {
            // 睡眠的分析数据
            if (dataLen >= 7 && dataLen + offset <= origin.length) {
              List<int> analysisDataArray =
                  origin.sublist(offset + 4, offset + 4 + dataLen);
              // 这里可以解析分析数据，暂时跳过
            }
          }
          offset += (dataLen + 4);
        }
      }
    }
    return entities;
  }
}
