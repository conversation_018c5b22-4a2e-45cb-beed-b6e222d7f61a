import 'dart:typed_data';

import '../models/health_entity.dart';
import '../utils/database_constants.dart';
import 'i_parser.dart';
import 'i_parser_modify.dart';
import 'parse_entity.dart';
import 'parse_entity_object.dart';
import 'parse_utils.dart';

/// 步数解析器实现
class StepParserImpl implements IParserModify<ParseEntity> {
  static const int DATA_TYPE_STEP = HealthDataType.step;

  @override
  List<ParseEntity> parse(List<HealthEntity> entities) {
    print('数据库StepParserImpl.parse: 开始解析，共${entities.length}条HealthEntity');
    List<ParseEntity> dataArray = [];
    for (HealthEntity healthEntity in entities) {
      print(
          '数据库StepParserImpl.parse: 解析HealthEntity: type=${healthEntity.type}, version=${healthEntity.version}, data.length=${healthEntity.data?.length}');
      List<ParseEntity> tempArray = ParserV0().parse(healthEntity);
      print('数据库StepParserImpl.parse: 解析结果${tempArray.length}条');
      dataArray.addAll(tempArray);
    }
    print('数据库StepParserImpl.parse: 解析完成，返回${dataArray.length}条ParseEntity');
    return dataArray;
  }
}

class ParserV0 implements IParser<ParseEntity> {
  @override
  List<ParseEntity> parse(HealthEntity entity) {
    print(
        '数据库ParserV0.parse: 开始解析HealthEntity, type=${entity.type}, data.length=${entity.data?.length}');
    List<ParseEntity> entities = [];
    Uint8List origin = entity.data.sublist(1); //去除长度位

    // 检查实体类型是否为步数类型2
    if (entity.type == StepParserImpl.DATA_TYPE_STEP && origin != null) {
      print('数据库ParserV0.parse: 满足步数类型和数据长度要求，开始解析');
      DateTime calendar = DateTime.now();
      int year = ParseUtils.bytesToInt(origin[1], origin[2]);
      int month = ParseUtils.byteToUnsignedInt(origin[3]);
      int day = ParseUtils.byteToUnsignedInt(origin[4]);
      int space = ParseUtils.byteToUnsignedInt(origin[8]) * 60 * 1000;
      int offset = 11;
      print(
          '数据库ParserV0.parse: 年=$year, 月=$month, 日=$day, space=$space, offset=$offset');

      while (offset + 4 <= origin.length) {
        List<int> tempArray = origin.sublist(offset, offset + 4);
        int hour = ParseUtils.byteToUnsignedInt(tempArray[0]);
        int minute = ParseUtils.byteToUnsignedInt(tempArray[1]);
        int dataLen = ParseUtils.bytesToInt(tempArray[2], tempArray[3]);
        print(
            '数据库ParserV0.parse: offset=$offset, hour=$hour, minute=$minute, dataLen=$dataLen');

        if (ParseUtils.isValidTime(hour, minute)) {
          if (offset + dataLen > origin.length) {
            print('数据库ParserV0.parse: 数据长度超出origin范围，跳出循环');
            break;
          }

          calendar = ParseUtils.createDateTime(year, month, day, hour, minute);
          List<int> data = origin.sublist(offset + 4, offset + 4 + dataLen);
          int time = ParseUtils.getTimestamp(calendar);
          print(
              '数据库ParserV0.parse: 解析时间点: $calendar, data.length=${data.length}, time=$time');

          for (int i = 0; i + 6 <= data.length; i += 6) {
            int step = ParseUtils.bytesToInt(data[i], data[i + 1]);
            int distance = ParseUtils.bytesToInt(data[i + 2], data[i + 3]);
            int cal = ParseUtils.bytesToInt(data[i + 4], data[i + 5]);
            print(
                '数据库ParserV0.parse: step=$step, distance=$distance, cal=$cal, time=$time');
            entities.add(ParseEntityObject(
                time, step.toDouble(), distance.toDouble(), cal.toDouble()));
            time += space;
          }
        } else {
          print('数据库ParserV0.parse: 无效时间 hour=$hour, minute=$minute，跳过');
        }
        offset += (dataLen + 4);
      }
    } else {
      print('数据库ParserV0.parse: 不满足步数类型或数据长度要求，跳过解析');
    }
    print('数据库ParserV0.parse: 解析完成，返回${entities.length}条ParseEntity');
    return entities;
  }
}
