/// 浮点数解析实体类 - 用于需要浮点数值的数据
class ParseEntityFloat {
  int _startTime;
  double _value;
  int _endTime;

  /// 构造函数 - 包含结束时间
  ParseEntityFloat(this._startTime, this._endTime, this._value);

  /// 构造函数 - 不包含结束时间
  ParseEntityFloat.withoutEndTime(this._startTime, this._value) : _endTime = 0;

  /// 默认构造函数
  ParseEntityFloat.empty()
      : _startTime = 0,
        _value = 0.0,
        _endTime = 0;

  /// 开始时间
  int get startTime => _startTime;
  set startTime(int value) => _startTime = value;

  /// 结束时间
  int get endTime => _endTime;
  set endTime(int value) => _endTime = value;

  /// 浮点数值
  double get value => _value;
  set value(double value) => _value = value;

  @override
  String toString() {
    return 'ParseEntityFloat{startTime: $_startTime, endTime: $_endTime, value: $_value}';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ParseEntityFloat &&
        other._startTime == _startTime &&
        other._endTime == _endTime &&
        other._value == _value;
  }

  @override
  int get hashCode {
    return _startTime.hashCode ^ _endTime.hashCode ^ _value.hashCode;
  }
}
