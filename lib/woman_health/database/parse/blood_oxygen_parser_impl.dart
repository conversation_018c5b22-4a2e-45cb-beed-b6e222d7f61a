import '../models/health_entity.dart';
import 'i_parser.dart';
import 'i_parser_modify.dart';
import 'parse_entity.dart';
import 'parse_utils.dart';

/// 血氧解析器实现
class BloodOxygenParserImpl implements IParserModify<ParseEntity> {
  static const int DATA_TYPE_BLOOD_OXYGEN = 0x04;

  @override
  List<ParseEntity> parse(List<HealthEntity> entities) {
    List<ParseEntity> dataArray = [];
    for (HealthEntity healthEntity in entities) {
      if (healthEntity.version == 0) {
        List<ParseEntity> tempArray = ParserV0().parse(healthEntity);
        dataArray.addAll(tempArray);
      }
    }
    return dataArray;
  }
}

class ParserV0 implements IParser<ParseEntity> {
  @override
  List<ParseEntity> parse(HealthEntity entity) {
    List<ParseEntity> entities = [];
    List<int>? origin = entity.data;

    if (origin != null && origin.length > 15) {
      if (origin[0] == BloodOxygenParserImpl.DATA_TYPE_BLOOD_OXYGEN) {
        DateTime calendar = DateTime.now();
        int year = ParseUtils.bytesToInt(origin[1], origin[2]);
        int month = ParseUtils.byteToUnsignedInt(origin[3]);
        int day = ParseUtils.byteToUnsignedInt(origin[4]);
        int space = ParseUtils.byteToUnsignedInt(origin[8]) * 60 * 1000;
        int offset = 11;

        while (offset + 4 <= origin.length) {
          List<int> tempArray = origin.sublist(offset, offset + 4);
          int hour = ParseUtils.byteToUnsignedInt(tempArray[0]);
          int minute = ParseUtils.byteToUnsignedInt(tempArray[1]);
          int dataLen = ParseUtils.bytesToInt(tempArray[2], tempArray[3]);

          if (ParseUtils.isValidTime(hour, minute)) {
            if (offset + dataLen > origin.length) {
              break;
            }

            calendar =
                ParseUtils.createDateTime(year, month, day, hour, minute);
            List<int> data = origin.sublist(offset + 4, offset + 4 + dataLen);
            int time = ParseUtils.getTimestamp(calendar);

            for (int i = 0; i < data.length; i++) {
              int bloodOxygen = ParseUtils.byteToUnsignedInt(data[i]);
              bloodOxygen = bloodOxygen > 0 ? bloodOxygen : 0;
              entities.add(
                  ParseEntity.withoutEndTime(time, bloodOxygen.toDouble()));
              time += space;
            }
          }
          offset += (dataLen + 4);
        }
      }
    }
    return entities;
  }
}
