import 'helpers/health_database_helper.dart';

// 数据转换器
export 'converters/string_converters.dart';
// 数据访问对象
export 'dao/health_dao.dart';
export 'dao/sport_record_dao.dart';
// 数据库服务
export 'database_service.dart';
// 数据库测试
export 'database_test.dart';
// 数据管理类
export 'health_data_manager.dart';
// 数据库帮助类
export 'helpers/health_database_helper.dart';
// 模型类
export 'models/health_entity.dart';
export 'models/sport_record.dart';
// 数据库常量
export 'utils/database_constants.dart';
// 平台工具
export 'utils/platform_helper.dart';
// 视图对象
export 'vo/base_vo.dart';
export 'vo/step/step_base_vo.dart';
export 'vo/step/step_day_vo.dart';

/// GsFit数据库主类
class GsFitDatabase {
  static final GsFitDatabase _instance = GsFitDatabase._internal();

  factory GsFitDatabase() {
    return _instance;
  }

  GsFitDatabase._internal();

  /// 获取健康数据库帮助类实例
  static HealthDatabaseHelper get healthDatabase =>
      HealthDatabaseHelper.instance;

  /// 初始化数据库
  static Future<void> initialize() async {
    try {
      await healthDatabase.database;
      print('healthDatabase.database数据库初始化成功');
    } catch (e) {
      print('healthDatabase.database数据库初始化失败: $e');
      rethrow;
    }
  }

  /// 关闭数据库
  static Future<void> close() async {
    try {
      await healthDatabase.close();
      print('healthDatabase.database数据库已关闭');
    } catch (e) {
      print('healthDatabase.database数据库关闭失败: $e');
    }
  }

  /// 删除数据库
  static Future<void> deleteDatabase() async {
    try {
      await healthDatabase.deleteDatabase();
      print('healthDatabase.database数据库已删除');
    } catch (e) {
      print('healthDatabase.database数据库删除失败: $e');
    }
  }
}
