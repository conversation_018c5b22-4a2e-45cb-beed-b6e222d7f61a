import '../models/health_entity.dart';

/// 基础视图对象抽象类
abstract class BaseVo {
  List<HealthEntity>? _healthEntities;
  int _startTime = 0;
  int _endTime = 0;

  /// 获取健康数据实体列表
  List<HealthEntity>? get healthEntities => _healthEntities;

  /// 设置健康数据实体列表
  set healthEntities(List<HealthEntity>? entities) {
    _healthEntities = entities;
    if (entities != null) {
      parse(entities);
    }
  }

  /// 开始时间
  int get startTime => _startTime;
  set startTime(int value) => _startTime = value;

  /// 结束时间
  int get endTime => _endTime;
  set endTime(int value) => _endTime = value;

  /// 在子类中解析数据
  void parse(List<HealthEntity> healthEntities);

  /// 获取数据类型
  int getType();

  /// 生成模拟数据
  List<HealthEntity> createTestData(int startTime, int endTime);

  /// 获取数据总数
  int get totalCount => _healthEntities?.length ?? 0;

  /// 检查是否有数据
  bool get hasData => _healthEntities != null && _healthEntities!.isNotEmpty;

  /// 获取第一个数据
  HealthEntity? get firstData =>
      _healthEntities?.isNotEmpty == true ? _healthEntities!.first : null;

  /// 获取最后一个数据
  HealthEntity? get lastData =>
      _healthEntities?.isNotEmpty == true ? _healthEntities!.last : null;

  /// 根据索引获取数据
  HealthEntity? getDataAt(int index) {
    if (_healthEntities != null &&
        index >= 0 &&
        index < _healthEntities!.length) {
      return _healthEntities![index];
    }
    return null;
  }

  /// 添加数据
  void addData(HealthEntity entity) {
    _healthEntities ??= [];
    _healthEntities!.add(entity);
    parse(_healthEntities!);
  }

  /// 添加多个数据
  void addAllData(List<HealthEntity> entities) {
    _healthEntities ??= [];
    _healthEntities!.addAll(entities);
    parse(_healthEntities!);
  }

  /// 清除数据
  void clearData() {
    _healthEntities?.clear();
  }

  /// 获取时间范围内的数据
  List<HealthEntity> getDataInTimeRange(int start, int end) {
    if (_healthEntities == null) return [];

    return _healthEntities!.where((entity) {
      return entity.time >= start && entity.time <= end;
    }).toList();
  }

  /// 获取数据类型名称
  String get typeName {
    switch (getType()) {
      case 0x01:
        return '心率';
      case 0x04:
        return '步数';
      case 0x05:
        return '睡眠';
      case 0x06:
        return '血氧';
      case 0xf4:
        return '压力';
      case 0xFF:
        return '体重';
      default:
        return '未知';
    }
  }

  @override
  String toString() {
    return 'BaseVo{type: ${getType()}, typeName: $typeName, startTime: $_startTime, endTime: $_endTime, dataCount: ${_healthEntities?.length ?? 0}}';
  }
}
