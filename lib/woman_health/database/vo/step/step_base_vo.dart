import '../../models/health_entity.dart';
import '../base_vo.dart';
import '../../utils/database_constants.dart';

/// 步数基础视图对象
abstract class StepBaseVo extends BaseVo {
  int _totalStep = 0;
  int _totalDistance = 0;
  int _totalKcal = 0;
  int _max = 0;
  int _highLightIndex = 0;

  /// 总步数
  int get totalStep => _totalStep;
  set totalStep(int value) => _totalStep = value;

  /// 总距离（米）
  int get totalDistance => _totalDistance;
  set totalDistance(int value) => _totalDistance = value;

  /// 总卡路里
  int get totalKcal => _totalKcal;
  set totalKcal(int value) => _totalKcal = value;

  /// 最大值
  int get max => _max;
  set max(int value) => _max = value;

  /// 高亮索引
  int get highLightIndex => _highLightIndex;
  set highLightIndex(int value) => _highLightIndex = value;

  @override
  int getType() => HealthDataType.step;

  /// 获取总距离（格式化）
  String get formattedTotalDistance {
    if (_totalDistance >= 1000) {
      return '${(_totalDistance / 1000).toStringAsFixed(2)}km';
    } else {
      return '${_totalDistance}m';
    }
  }

  /// 获取总卡路里（格式化）
  String get formattedTotalKcal {
    return '${_totalKcal}kcal';
  }

  /// 获取步数（格式化）
  String get formattedTotalStep {
    return '$_totalStep步';
  }

  /// 获取最大值（格式化）
  String get formattedMax {
    return '$_max步';
  }

  /// 重置统计数据
  void resetStatistics() {
    _totalStep = 0;
    _totalDistance = 0;
    _totalKcal = 0;
    _max = 0;
    _highLightIndex = 0;
  }

  /// 计算统计数据
  void calculateStatistics() {
    resetStatistics();

    if (healthEntities == null || healthEntities!.isEmpty) return;

    for (final entity in healthEntities!) {
      // 这里需要根据具体的步数数据解析逻辑来计算
      // 暂时使用简单的累加逻辑
      _totalStep += entity.space; // 假设space字段存储步数
      _max = _max > entity.space ? _max : entity.space;
    }
  }

  @override
  void parse(List<HealthEntity> healthEntities) {
    // 子类需要实现具体的解析逻辑
    calculateStatistics();
  }

  @override
  String toString() {
    return 'StepBaseVo{totalStep: $_totalStep, totalDistance: $_totalDistance, totalKcal: $_totalKcal, max: $_max, highLightIndex: $_highLightIndex}';
  }
}
