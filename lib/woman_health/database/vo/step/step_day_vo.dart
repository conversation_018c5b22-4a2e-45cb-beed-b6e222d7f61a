import 'dart:typed_data';
import '../../models/health_entity.dart';
import '../../utils/database_constants.dart';
import 'step_base_vo.dart';

/// 步数图表数据
class StepChartData {
  int index = 0;
  int value = 0;

  StepChartData({required this.index, required this.value});

  @override
  String toString() {
    return 'StepChartData{index: $index, value: $value}';
  }
}

/// 日步数视图对象（24小时数据，以小时为间隔）
class StepDayVo extends StepBaseVo {
  List<StepChartData> _chartData = [];

  /// 图表数据
  List<StepChartData> get chartData => _chartData;

  @override
  void parse(List<HealthEntity> healthEntities) {
    super.parse(healthEntities);
    _parseStepData(healthEntities);
  }

  /// 解析步数数据
  void _parseStepData(List<HealthEntity> entities) {
    _chartData.clear();

    if (entities.isEmpty) return;

    // 24小时数据，每小时一个数据点
    const int dataLen = 24;
    List<int> stepArray = List.filled(dataLen, 0);

    // 默认高亮中间位置
    highLightIndex = dataLen ~/ 2;

    // 解析每个健康数据实体
    for (final entity in entities) {
      // 这里需要根据具体的数据格式来解析
      // 暂时使用简单的逻辑
      final hour = _getHourFromTimestamp(entity.time);
      if (hour >= 0 && hour < dataLen) {
        final stepCount = entity.space; // 假设space字段存储步数
        stepArray[hour] += stepCount;

        if (stepCount > 0) {
          highLightIndex = hour + 1;
          totalStep += stepCount;
          max = max > stepCount ? max : stepCount;
        }
      }
    }

    // 转换为图表数据
    for (int i = 0; i < stepArray.length; i++) {
      _chartData.add(StepChartData(
        index: i + 1,
        value: stepArray[i],
      ));
    }
  }

  /// 从时间戳获取小时
  int _getHourFromTimestamp(int timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return date.hour;
  }

  @override
  List<HealthEntity> createTestData(int startTime, int endTime) {
    final List<HealthEntity> healthEntities = [];

    // 创建模拟的24小时步数数据
    for (int hour = 0; hour < 24; hour++) {
      final entity = HealthEntity(
        uid: 'test_user',
        type: HealthDataType.step,
        version: 0,
        time: startTime + (hour * 3600), // 每小时递增
        space:
            (100 + (hour * 50) + (DateTime.now().millisecondsSinceEpoch % 200))
                .toInt(), // 模拟步数
        sync: false,
        data: Uint8List.fromList(
            [0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00]), // 模拟数据
      );
      healthEntities.add(entity);
    }

    return healthEntities;
  }

  /// 获取指定小时的数据
  StepChartData? getDataAtHour(int hour) {
    if (hour >= 0 && hour < _chartData.length) {
      return _chartData[hour];
    }
    return null;
  }

  /// 获取最高步数的小时
  int get maxStepHour {
    if (_chartData.isEmpty) return 0;

    int maxIndex = 0;
    int maxValue = _chartData[0].value;

    for (int i = 1; i < _chartData.length; i++) {
      if (_chartData[i].value > maxValue) {
        maxValue = _chartData[i].value;
        maxIndex = i;
      }
    }

    return maxIndex;
  }

  /// 获取平均步数
  double get averageStep {
    if (_chartData.isEmpty) return 0.0;

    int total = 0;
    for (final data in _chartData) {
      total += data.value;
    }

    return total / _chartData.length;
  }

  /// 获取活跃小时数（步数大于0的小时）
  int get activeHours {
    return _chartData.where((data) => data.value > 0).length;
  }

  @override
  String toString() {
    return 'StepDayVo{chartData: ${_chartData.length} items, totalStep: $totalStep, max: $max, highLightIndex: $highLightIndex}';
  }
}
