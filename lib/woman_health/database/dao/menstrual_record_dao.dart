import 'dart:typed_data';
import 'package:sqflite/sqflite.dart';
import '../../models/menstrual_record.dart';
import '../utils/database_constants.dart';

/// 月经记录数据访问对象
class MenstrualRecordDao {
  final Future<Database> _database;

  MenstrualRecordDao(this._database);

  /// 插入月经记录
  Future<int> insert(MenstrualRecord record) async {
    final db = await _database;
    return await db.insert(
      TableNames.menstrualRecord,
      _toMap(record),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// 批量插入月经记录
  Future<void> insertBatch(List<MenstrualRecord> records) async {
    final db = await _database;
    final batch = db.batch();
    
    for (final record in records) {
      batch.insert(
        TableNames.menstrualRecord,
        _toMap(record),
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    }
    
    await batch.commit(noResult: true);
  }

  /// 更新月经记录
  Future<int> update(MenstrualRecord record) async {
    final db = await _database;
    return await db.update(
      TableNames.menstrualRecord,
      _toMap(record),
      where: 'id = ?',
      whereArgs: [record.id],
    );
  }

  /// 删除月经记录
  Future<int> delete(int id) async {
    final db = await _database;
    return await db.delete(
      TableNames.menstrualRecord,
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// 根据用户ID删除所有记录
  Future<int> deleteByUid(String uid) async {
    final db = await _database;
    return await db.delete(
      TableNames.menstrualRecord,
      where: 'uid = ?',
      whereArgs: [uid],
    );
  }

  /// 根据ID查询月经记录
  Future<MenstrualRecord?> findById(int id) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.menstrualRecord,
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return _fromMap(maps.first);
    }
    return null;
  }

  /// 根据用户ID查询所有月经记录
  Future<List<MenstrualRecord>> findByUid(String uid) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.menstrualRecord,
      where: 'uid = ?',
      whereArgs: [uid],
      orderBy: 'time DESC',
    );

    return maps.map((map) => _fromMap(map)).toList();
  }

  /// 根据日期查询月经记录
  Future<MenstrualRecord?> findByDate(String uid, int year, int month, int day) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.menstrualRecord,
      where: 'uid = ? AND year = ? AND month = ? AND day = ?',
      whereArgs: [uid, year, month, day],
    );

    if (maps.isNotEmpty) {
      return _fromMap(maps.first);
    }
    return null;
  }

  /// 根据时间范围查询月经记录
  Future<List<MenstrualRecord>> findByTimeRange(
    String uid,
    int startTime,
    int endTime,
  ) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.menstrualRecord,
      where: 'uid = ? AND time >= ? AND time <= ?',
      whereArgs: [uid, startTime, endTime],
      orderBy: 'time ASC',
    );

    return maps.map((map) => _fromMap(map)).toList();
  }

  /// 查询月经记录（有出血的记录）
  Future<List<MenstrualRecord>> findMenstruationRecords(String uid) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.menstrualRecord,
      where: 'uid = ? AND isMenstruation = 1',
      whereArgs: [uid],
      orderBy: 'time DESC',
    );

    return maps.map((map) => _fromMap(map)).toList();
  }

  /// 查询最近的月经记录
  Future<MenstrualRecord?> findLatestMenstruationRecord(String uid) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.menstrualRecord,
      where: 'uid = ? AND isMenstruation = 1',
      whereArgs: [uid],
      orderBy: 'time DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return _fromMap(maps.first);
    }
    return null;
  }

  /// 查询指定月份的月经记录
  Future<List<MenstrualRecord>> findByMonth(String uid, int year, int month) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.menstrualRecord,
      where: 'uid = ? AND year = ? AND month = ?',
      whereArgs: [uid, year, month],
      orderBy: 'day ASC',
    );

    return maps.map((map) => _fromMap(map)).toList();
  }

  /// 查询有症状的记录
  Future<List<MenstrualRecord>> findRecordsWithSymptoms(String uid) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.menstrualRecord,
      where: 'uid = ? AND symptom > 0',
      whereArgs: [uid],
      orderBy: 'time DESC',
    );

    return maps.map((map) => _fromMap(map)).toList();
  }

  /// 统计月经记录数量
  Future<int> countByUid(String uid) async {
    final db = await _database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${TableNames.menstrualRecord} WHERE uid = ?',
      [uid],
    );
    return result.first['count'] as int;
  }

  /// 统计月经天数
  Future<int> countMenstruationDays(String uid) async {
    final db = await _database;
    final result = await db.rawQuery(
      'SELECT COUNT(*) as count FROM ${TableNames.menstrualRecord} WHERE uid = ? AND isMenstruation = 1',
      [uid],
    );
    return result.first['count'] as int;
  }

  /// 将MenstrualRecord对象转换为Map
  Map<String, dynamic> _toMap(MenstrualRecord record) {
    return {
      'id': record.id,
      'uid': record.uid,
      'time': record.time,
      'day': record.day,
      'month': record.month,
      'year': record.year,
      'symptom': record.symptom,
      'data': Uint8List.fromList(record.data),
      'isDropletBleeding': record.isDropletBleeding ? 1 : 0,
      'isMenstruation': record.isMenstruation ? 1 : 0,
      'bleedingVolume': record.bleedingVolume,
    };
  }

  /// 将Map转换为MenstrualRecord对象
  MenstrualRecord _fromMap(Map<String, dynamic> map) {
    return MenstrualRecord(
      id: map['id'] as int?,
      uid: map['uid'] as String,
      time: map['time'] as int,
      day: map['day'] as int,
      month: map['month'] as int,
      year: map['year'] as int,
      symptom: map['symptom'] as int,
      data: (map['data'] as Uint8List?)?.toList() ?? [],
      isDropletBleeding: (map['isDropletBleeding'] as int) == 1,
      isMenstruation: (map['isMenstruation'] as int) == 1,
      bleedingVolume: map['bleedingVolume'] as int,
    );
  }
}
