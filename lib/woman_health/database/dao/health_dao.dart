import 'dart:async';
import 'dart:typed_data';

import 'package:sqflite/sqlite_api.dart';

import '../models/health_entity.dart';
import '../utils/database_constants.dart';

/// 健康数据访问对象
class HealthDao {
  final Future<Database> _database;

  HealthDao(this._database);

  /// 删除所有健康数据
  Future<void> deleteAll() async {
    final db = await _database;
    await db.delete(TableNames.healthEntity);
  }

  /// 插入健康数据
  Future<void> insert(HealthEntity healthEntity) async {
    final db = await _database;
    await db.insert(
      TableNames.healthEntity,
      _healthEntityToMap(healthEntity),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// 根据ID查找健康数据
  Future<HealthEntity?> findHealthById(int type, String uid, int id) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.healthEntity,
      where: 'type = ? AND uid = ? AND id = ?',
      whereArgs: [type, uid, id],
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return _mapToHealthEntity(maps.first);
    }
    return null;
  }

  /// 按日期范围查询健康数据
  Future<List<HealthEntity>> findHealthByDate(
      int type, String uid, int start, int end) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.healthEntity,
      where: 'type = ? AND uid = ? AND time >= ? AND time < ?',
      whereArgs: [type, uid, start, end],
      orderBy: 'time ASC',
    );

    return maps.map((map) => _mapToHealthEntity(map)).toList();
  }

  /// 查询睡眠统计数据
  Future<List<int>> findSleepCountData(String uid, int start, int end) async {
    final db = await _database;

    // 复杂的睡眠数据查询SQL
    final sql = '''
      SELECT ifnull(tempp.space,0)
      FROM (
        SELECT 0 h FROM sqlite_master union
        SELECT 1 h FROM sqlite_master UNION
        SELECT 2 h FROM sqlite_master UNION
        SELECT 3 h FROM sqlite_master UNION
        SELECT 4 h FROM sqlite_master UNION
        SELECT 5 h FROM sqlite_master UNION
        SELECT 6 h FROM sqlite_master UNION
        SELECT 7 h FROM sqlite_master UNION
        SELECT 8 h FROM sqlite_master UNION
        SELECT 9 h FROM sqlite_master UNION
        SELECT 10 h FROM sqlite_master UNION
        SELECT 11 h FROM sqlite_master UNION
        SELECT 12 h FROM sqlite_master UNION
        SELECT 13 h FROM sqlite_master UNION
        SELECT 14 h FROM sqlite_master UNION
        SELECT 15 h FROM sqlite_master UNION
        SELECT 16 h FROM sqlite_master UNION
        SELECT 17 h FROM sqlite_master UNION
        SELECT 18 h FROM sqlite_master UNION
        SELECT 19 h FROM sqlite_master UNION
        SELECT 20 h FROM sqlite_master UNION
        SELECT 21 h FROM sqlite_master UNION
        SELECT 22 h FROM sqlite_master UNION
        SELECT 23 h FROM sqlite_master UNION
        SELECT 24 h FROM sqlite_master UNION
        SELECT 25 h FROM sqlite_master UNION
        SELECT 26 h FROM sqlite_master UNION
        SELECT 27 h FROM sqlite_master UNION
        SELECT 28 h FROM sqlite_master UNION
        SELECT 29 h FROM sqlite_master UNION
        SELECT 30 h FROM sqlite_master UNION
        SELECT 31 h FROM sqlite_master UNION
        SELECT 32 h FROM sqlite_master UNION
        SELECT 33 h FROM sqlite_master UNION
        SELECT 34 h FROM sqlite_master UNION
        SELECT 35 h FROM sqlite_master UNION
        SELECT 36 h FROM sqlite_master UNION
        SELECT 37 h FROM sqlite_master UNION
        SELECT 38 h FROM sqlite_master UNION
        SELECT 39 h FROM sqlite_master UNION
        SELECT 40 h FROM sqlite_master UNION
        SELECT 41 h FROM sqlite_master UNION
        SELECT 42 h FROM sqlite_master UNION
        SELECT 43 h FROM sqlite_master UNION
        SELECT 44 h FROM sqlite_master UNION
        SELECT 45 h FROM sqlite_master UNION
        SELECT 46 h FROM sqlite_master UNION
        SELECT 47 h FROM sqlite_master 
      ) temph left join 
      (
        select (STRFTIME('%H',datetime(HealthEntity.time/1000, 'unixepoch', 'localtime'))*60 + 
        STRFTIME('%M',datetime(HealthEntity.time/1000, 'unixepoch', 'localtime')))/30 sleepIndex,HealthEntity.space from HealthEntity 
        where HealthEntity.type = 5 and uid = ? and HealthEntity.time >= ? and HealthEntity.time < ? 
      ) tempp 
      on temph.h = tempp.sleepIndex
      GROUP BY temph.h
      order by temph.h
    ''';

    final List<Map<String, dynamic>> result =
        await db.rawQuery(sql, [uid, start, end]);
    return result.map((row) => row.values.first as int).toList();
  }

  /// 获取指定时间范围内的最新健康数据
  Future<HealthEntity?> findHealthByDateCommonLast(
      int type, String uid, int start, int end) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.healthEntity,
      where: 'type = ? AND uid = ? AND time >= ? AND time < ?',
      whereArgs: [type, uid, start, end],
      orderBy: 'time DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return _mapToHealthEntity(maps.first);
    }
    return null;
  }

  /// 获取最新的健康数据
  Future<HealthEntity?> getHealthLiveDataLast(int type, String uid) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.healthEntity,
      where: 'type = ? AND uid = ?',
      whereArgs: [type, uid],
      orderBy: 'id DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return _mapToHealthEntity(maps.first);
    }
    return null;
  }

  /// 获取指定时间后的最新数据
  Future<HealthEntity?> getTodayData(String uid, int type, int time) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.healthEntity,
      where: 'uid = ? AND type = ? AND id > 0 AND time >= ?',
      whereArgs: [uid, type, time],
      orderBy: 'time DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return _mapToHealthEntity(maps.first);
    }
    return null;
  }

  /// 统计每日健康数据
  Future<List<HealthEntity>> countDailyHealthData(
      String uid, int type, int start, int end) async {
    final db = await _database;
    final sql = '''
      SELECT id, uid, type, version, crcCode, space, sync, data, MAX(time) time 
      FROM ${TableNames.healthEntity}
      WHERE type = ? AND uid = ? 
      AND time/1000 BETWEEN ? AND ? 
      GROUP BY STRFTIME('%Y%m%d',datetime(HealthEntity.time/1000, 'unixepoch', 'localtime'))
    ''';

    final List<Map<String, dynamic>> maps =
        await db.rawQuery(sql, [type, uid, start, end]);
    return maps.map((map) => _mapToHealthEntity(map)).toList();
  }

  /// 根据同步状态查询数据
  Future<List<HealthEntity>> findBySync(String uid, bool sync) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.healthEntity,
      where: 'uid = ? AND sync = ?',
      whereArgs: [uid, sync ? 1 : 0],
    );

    return maps.map((map) => _mapToHealthEntity(map)).toList();
  }

  /// 清理所有数据
  Future<void> clean() async {
    final db = await _database;
    await db.delete(TableNames.healthEntity);
  }

  /// 将HealthEntity转换为Map
  Map<String, dynamic> _healthEntityToMap(HealthEntity entity) {
    return {
      'id': entity.id,
      'uid': entity.uid,
      'type': entity.type,
      'version': entity.version,
      'time': entity.time,
      'crcCode': entity.crcCode,
      'space': entity.space,
      'sync': entity.sync ? 1 : 0,
      'data': entity.data,
    };
  }

  /// 将Map转换为HealthEntity
  HealthEntity _mapToHealthEntity(Map<String, dynamic> map) {
    return HealthEntity(
      id: map['id'] as int?,
      uid: map['uid'] as String,
      type: map['type'] as int,
      version: map['version'] as int,
      time: map['time'] as int,
      crcCode: map['crcCode'] as Uint8List?,
      space: map['space'] as int,
      sync: (map['sync'] as int) == 1,
      data: map['data'] as Uint8List,
    );
  }
}
