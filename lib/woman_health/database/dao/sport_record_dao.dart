import 'dart:async';
import 'dart:typed_data';

import 'package:sqflite/sqlite_api.dart';

import '../models/sport_record.dart';
import '../utils/database_constants.dart';

/// 运动记录数据访问对象
class SportRecordDao {
  final Future<Database> _database;

  SportRecordDao(this._database);

  /// 插入运动记录
  Future<void> insert(SportRecord record) async {
    final db = await _database;
    await db.insert(
      TableNames.sportRecord,
      _sportRecordToMap(record),
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  /// 根据用户ID查询运动记录
  Future<List<SportRecord>> findByUid(String uid) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.sportRecord,
      where: 'uid = ?',
      whereArgs: [uid],
      orderBy: 'startTime DESC',
    );

    return maps.map((map) => _mapToSportRecord(map)).toList();
  }

  /// 根据时间范围查询运动记录
  Future<List<SportRecord>> findByDateRange(
      String uid, int startTime, int endTime) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.sportRecord,
      where: 'uid = ? AND startTime >= ? AND startTime <= ?',
      whereArgs: [uid, startTime, endTime],
      orderBy: 'startTime DESC',
    );

    return maps.map((map) => _mapToSportRecord(map)).toList();
  }

  /// 根据运动类型查询记录
  Future<List<SportRecord>> findByType(String uid, int type) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.sportRecord,
      where: 'uid = ? AND type = ?',
      whereArgs: [uid, type],
      orderBy: 'startTime DESC',
    );

    return maps.map((map) => _mapToSportRecord(map)).toList();
  }

  /// 获取最新的运动记录
  Future<SportRecord?> getLatestRecord(String uid) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.sportRecord,
      where: 'uid = ?',
      whereArgs: [uid],
      orderBy: 'startTime DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return _mapToSportRecord(maps.first);
    }
    return null;
  }

  /// 根据同步状态查询记录
  Future<List<SportRecord>> findBySync(String uid, bool sync) async {
    final db = await _database;
    final List<Map<String, dynamic>> maps = await db.query(
      TableNames.sportRecord,
      where: 'uid = ? AND sync = ?',
      whereArgs: [uid, sync ? 1 : 0],
      orderBy: 'startTime DESC',
    );

    return maps.map((map) => _mapToSportRecord(map)).toList();
  }

  /// 更新运动记录
  Future<void> update(SportRecord record) async {
    final db = await _database;
    await db.update(
      TableNames.sportRecord,
      _sportRecordToMap(record),
      where: 'uid = ? AND startTime = ?',
      whereArgs: [record.uid, record.startTime],
    );
  }

  /// 删除运动记录
  Future<void> delete(String uid, int startTime) async {
    final db = await _database;
    await db.delete(
      TableNames.sportRecord,
      where: 'uid = ? AND startTime = ?',
      whereArgs: [uid, startTime],
    );
  }

  /// 删除用户的所有运动记录
  Future<void> deleteByUid(String uid) async {
    final db = await _database;
    await db.delete(
      TableNames.sportRecord,
      where: 'uid = ?',
      whereArgs: [uid],
    );
  }

  /// 统计运动数据
  Future<Map<String, dynamic>> getStatistics(
      String uid, int startTime, int endTime) async {
    final db = await _database;
    final sql = '''
      SELECT 
        COUNT(*) as totalRecords,
        SUM(duration) as totalDuration,
        SUM(distance) as totalDistance,
        SUM(kcal) as totalKcal,
        SUM(step) as totalSteps
      FROM ${TableNames.sportRecord}
      WHERE uid = ? AND startTime >= ? AND startTime <= ?
    ''';

    final List<Map<String, dynamic>> result =
        await db.rawQuery(sql, [uid, startTime, endTime]);

    if (result.isNotEmpty) {
      final row = result.first;
      return {
        'totalRecords': row['totalRecords'] ?? 0,
        'totalDuration': row['totalDuration'] ?? 0,
        'totalDistance': row['totalDistance'] ?? 0,
        'totalKcal': row['totalKcal'] ?? 0,
        'totalSteps': row['totalSteps'] ?? 0,
      };
    }

    return {
      'totalRecords': 0,
      'totalDuration': 0,
      'totalDistance': 0,
      'totalKcal': 0,
      'totalSteps': 0,
    };
  }

  /// 将SportRecord转换为Map
  Map<String, dynamic> _sportRecordToMap(SportRecord record) {
    return {
      'uid': record.uid,
      'startTime': record.startTime,
      'type': record.type,
      'internal': record.internal,
      'version': record.version,
      'reserve': record.reserve,
      'duration': record.duration,
      'stopTime': record.stopTime,
      'distance': record.distance,
      'kcal': record.kcal,
      'step': record.step,
      'recoveryTime': record.recoveryTime,
      'sync': record.sync ? 1 : 0,
      'data': record.data,
    };
  }

  /// 将Map转换为SportRecord
  SportRecord _mapToSportRecord(Map<String, dynamic> map) {
    return SportRecord(
      uid: map['uid'] as String,
      startTime: map['startTime'] as int,
      type: map['type'] as int,
      internal: map['internal'] as int,
      version: map['version'] as int,
      reserve: map['reserve'] as Uint8List?,
      duration: map['duration'] as int,
      stopTime: map['stopTime'] as int,
      distance: map['distance'] as int,
      kcal: map['kcal'] as int,
      step: map['step'] as int,
      recoveryTime: map['recoveryTime'] as int,
      sync: (map['sync'] as int) == 1,
      data: map['data'] as Uint8List?,
    );
  }
}
