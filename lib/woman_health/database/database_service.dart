import 'package:get/get.dart';
import 'gsfit_database.dart';
import 'helpers/health_database_helper.dart';
import 'utils/platform_helper.dart';

/// 数据库服务类
class DatabaseService extends GetxService {
  static DatabaseService? _instance;

  DatabaseService._();

  static DatabaseService get instance {
    _instance ??= DatabaseService._();
    return _instance!;
  }

  /// 初始化数据库
  Future<void> init() async {
    try {
      print('DatabaseService: 开始初始化数据库，当前平台: ${PlatformHelper.platformName}');
      print('DatabaseService: 使用数据库包: ${PlatformHelper.databasePackage}');

      await GsFitDatabase.initialize();
      print('DatabaseService: 数据库初始化成功');
    } catch (e) {
      print('DatabaseService: 数据库初始化失败: $e');

      // 如果是鸿蒙系统且初始化失败，尝试使用备用方案
      if (PlatformHelper.isOhos) {
        print('DatabaseService: 鸿蒙系统数据库初始化失败，尝试备用方案');
        await _initFallback();
      } else {
        rethrow;
      }
    }
  }

  /// 备用初始化方案
  Future<void> _initFallback() async {
    try {
      // 这里可以实现备用数据库方案
      // 比如使用内存数据库或者文件存储
      print('DatabaseService: 使用备用数据库方案');

      // 暂时抛出异常，表示备用方案也未实现
      throw Exception('备用数据库方案未实现');
    } catch (e) {
      print('DatabaseService: 备用数据库方案也失败: $e');
      rethrow;
    }
  }

  /// 获取健康数据库帮助类
  HealthDatabaseHelper get healthDatabase => GsFitDatabase.healthDatabase;

  /// 关闭数据库
  Future<void> close() async {
    try {
      await GsFitDatabase.close();
      print('DatabaseService: 数据库已关闭');
    } catch (e) {
      print('DatabaseService: 关闭数据库失败: $e');
    }
  }

  /// 删除数据库
  Future<void> deleteDatabase() async {
    try {
      await GsFitDatabase.deleteDatabase();
      print('DatabaseService: 数据库已删除');
    } catch (e) {
      print('DatabaseService: 删除数据库失败: $e');
    }
  }

  /// 检查数据库是否可用
  bool get isDatabaseAvailable {
    try {
      // 尝试获取数据库实例，如果成功则说明数据库可用
      return true;
    } catch (e) {
      return false;
    }
  }
}
