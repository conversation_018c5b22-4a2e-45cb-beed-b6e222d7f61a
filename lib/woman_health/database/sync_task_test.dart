/*
import 'dart:typed_data';

/// 同步任务测试类
class SyncTaskTest {
  /// 测试步数同步任务
  static Future<void> testStepSyncTask() async {
    print('=== 开始测试步数同步任务 ===');

    try {
      // 创建步数同步任务
      final stepSyncTask = StepSyncTask();

      // 模拟步数数据
      final stepData = _createMockStepData();

      // 测试 isInLocal 方法
      final isInLocal = stepSyncTask.isInLocal(stepData);
      print('步数数据是否在本地: $isInLocal');

      // 测试 saveToDb 方法
      stepSyncTask.saveToDb(stepData);

      print('步数同步任务测试完成');
    } catch (e) {
      print('步数同步任务测试失败: $e');
    }
  }

  /// 测试心率同步任务
  static Future<void> testHeartRateSyncTask() async {
    print('=== 开始测试心率同步任务 ===');

    try {
      // 创建心率同步任务
      final heartRateSyncTask = HeartRateSyncTask();

      // 模拟心率数据
      final heartRateData = _createMockHeartRateData();

      // 测试 isInLocal 方法
      final isInLocal = heartRateSyncTask.isInLocal(heartRateData);
      print('心率数据是否在本地: $isInLocal');

      // 测试 saveToDb 方法
      heartRateSyncTask.saveToDb(heartRateData);

      print('心率同步任务测试完成');
    } catch (e) {
      print('心率同步任务测试失败: $e');
    }
  }

  /// 测试数据库查询功能
  static Future<void> testDatabaseQueries() async {
    print('=== 开始测试数据库查询功能 ===');

    try {
      final healthDao = GsFitDatabase.healthDatabase.healthDao;

      // 查询今天的步数数据
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = startOfDay.add(Duration(days: 1));

      final stepData = await healthDao.findHealthByDate(
        HealthDataType.step,
        '319064735532187648',
        startOfDay.millisecondsSinceEpoch,
        endOfDay.millisecondsSinceEpoch,
      );

      print('今天的步数数据: ${stepData.length} 条');

      // 查询今天的心率数据
      final heartRateData = await healthDao.findHealthByDate(
        HealthDataType.heartRate,
        '319064735532187648',
        startOfDay.millisecondsSinceEpoch,
        endOfDay.millisecondsSinceEpoch,
      );

      print('今天的心率数据: ${heartRateData.length} 条');

      // 验证数据完整性
      if (stepData.isNotEmpty) {
        final stepEntity = stepData.first;
        print(
            '步数数据验证: ID=${stepEntity.id}, 时间戳=${stepEntity.time}, 数据长度=${stepEntity.data.length}');
      }

      if (heartRateData.isNotEmpty) {
        final heartRateEntity = heartRateData.first;
        print(
            '心率数据验证: ID=${heartRateEntity.id}, 时间戳=${heartRateEntity.time}, 数据长度=${heartRateEntity.data.length}');
      }

      print('数据库查询功能测试完成');
    } catch (e) {
      print('数据库查询功能测试失败: $e');
    }
  }

  /// 创建模拟步数数据
  static List<int> _createMockStepData() {
    // 模拟步数数据的字节数组
    // 根据HealthEntity.fromBytes的解析逻辑构造数据
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch ~/ 1000; // 转换为秒

    List<int> data = [];

    // 命令长度 (1字节) - 不包括该字节
    data.add(25); // 假设总长度为26字节

    // 数据类型 (1字节) - 步数
    data.add(HealthDataType.step);

    // 创建时间 (4字节，小端序)
    data.add(timestamp & 0xFF);
    data.add((timestamp >> 8) & 0xFF);
    data.add((timestamp >> 16) & 0xFF);
    data.add((timestamp >> 24) & 0xFF);

    // 版本 (1字节)
    data.add(1);

    // CRC码 (2字节)
    data.add(0x12);
    data.add(0x34);

    // 步数值 (4字节)
    final steps = 8000; // 模拟8000步
    data.add(steps & 0xFF);
    data.add((steps >> 8) & 0xFF);
    data.add((steps >> 16) & 0xFF);
    data.add((steps >> 24) & 0xFF);

    // 其他字段...
    data.addAll(List.filled(12, 0)); // 填充剩余字段

    return data;
  }

  /// 创建模拟心率数据
  static List<int> _createMockHeartRateData() {
    // 模拟心率数据的字节数组
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch ~/ 1000; // 转换为秒

    List<int> data = [];

    // 命令长度 (1字节) - 不包括该字节
    data.add(20); // 假设总长度为21字节

    // 数据类型 (1字节) - 心率
    data.add(HealthDataType.heartRate);

    // 创建时间 (4字节，小端序)
    data.add(timestamp & 0xFF);
    data.add((timestamp >> 8) & 0xFF);
    data.add((timestamp >> 16) & 0xFF);
    data.add((timestamp >> 24) & 0xFF);

    // 版本 (1字节)
    data.add(1);

    // CRC码 (2字节)
    data.add(0x56);
    data.add(0x78);

    // 心率值 (1字节)
    final heartRate = 75; // 模拟75次/分钟
    data.add(heartRate);

    // 间隔 (1字节，在最后)
    data.add(1); // 1分钟间隔

    // 其他字段...
    data.addAll(List.filled(10, 0)); // 填充剩余字段

    return data;
  }

  /// 测试数据解析功能
  static Future<void> testDataParsing() async {
    print('=== 开始测试数据解析功能 ===');

    try {
      // 测试步数数据解析
      final stepData = _createMockStepData();
      final stepEntity = HealthEntity.fromBytes(Uint8List.fromList(stepData));

      if (stepEntity != null) {
        print('步数数据解析成功:');
        print('  ID: ${stepEntity.id}');
        print('  类型: ${stepEntity.typeName} (${stepEntity.type})');
        print('  时间戳: ${stepEntity.time}');
        print('  版本: ${stepEntity.version}');
        print('  间隔: ${stepEntity.space}');
        print('  同步状态: ${stepEntity.sync}');
        print('  数据长度: ${stepEntity.data.length}');
        print('  CRC码: ${stepEntity.crcCode?.toList()}');
        print('  用户ID: ${stepEntity.uid}');
      } else {
        print('步数数据解析失败');
      }

      // 测试心率数据解析
      final heartRateData = _createMockHeartRateData();
      final heartRateEntity =
          HealthEntity.fromBytes(Uint8List.fromList(heartRateData));

      if (heartRateEntity != null) {
        print('心率数据解析成功:');
        print('  ID: ${heartRateEntity.id}');
        print('  类型: ${heartRateEntity.typeName} (${heartRateEntity.type})');
        print('  时间戳: ${heartRateEntity.time}');
        print('  版本: ${heartRateEntity.version}');
        print('  间隔: ${heartRateEntity.space}');
        print('  同步状态: ${heartRateEntity.sync}');
        print('  数据长度: ${heartRateEntity.data.length}');
        print('  CRC码: ${heartRateEntity.crcCode?.toList()}');
        print('  用户ID: ${heartRateEntity.uid}');
      } else {
        print('心率数据解析失败');
      }

      print('数据解析功能测试完成');
    } catch (e) {
      print('数据解析功能测试失败: $e');
    }
  }

  /// 运行所有测试
  static Future<void> runAllTests() async {
    print('开始运行同步任务测试...');

    await testDataParsing();
    await testStepSyncTask();
    await testHeartRateSyncTask();
    await testDatabaseQueries();

    print('所有测试完成');
  }
}
*/
