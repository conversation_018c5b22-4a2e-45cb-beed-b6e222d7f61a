import 'dart:typed_data';
import 'package:get/get.dart';
import 'models/health_entity.dart';
import 'dao/health_dao.dart';
import 'utils/database_constants.dart';

/// 健康数据管理类
class HealthDataManager extends GetxController {
  static HealthDataManager? _instance;

  HealthDataManager._();

  static HealthDataManager get instance {
    _instance ??= HealthDataManager._();
    return _instance!;
  }

  late HealthDao _healthDao;

  @override
  void onInit() {
    super.onInit();
    _initDao();
  }

  void _initDao() {
    // 这里需要从数据库服务获取DAO实例
    // 暂时使用空实现，实际使用时需要注入
  }

  /// 插入健康数据
  Future<void> insertHealthData(HealthEntity healthEntity) async {
    try {
      await _healthDao.insert(healthEntity);
      print('健康数据插入成功: ${healthEntity.typeName}');
    } catch (e) {
      print('健康数据插入失败: $e');
      rethrow;
    }
  }

  /// 从原始字节数据创建并插入健康数据
  Future<HealthEntity?> insertFromBytes(Uint8List data, String uid) async {
    try {
      final healthEntity = HealthEntity.fromBytes(data);
      if (healthEntity != null) {
        final entityWithUid = healthEntity.copyWith(uid: uid);
        await insertHealthData(entityWithUid);
        return entityWithUid;
      }
    } catch (e) {
      print('从字节数据创建健康数据失败: $e');
    }
    return null;
  }

  /// 查询指定时间范围的心率数据
  Future<List<HealthEntity>> getHeartRateData(
      String uid, int startTime, int endTime) async {
    try {
      return await _healthDao.findHealthByDate(
          HealthDataType.heartRate, uid, startTime, endTime);
    } catch (e) {
      print('查询心率数据失败: $e');
      return [];
    }
  }

  /// 查询指定时间范围的步数数据
  Future<List<HealthEntity>> getStepData(
      String uid, int startTime, int endTime) async {
    try {
      return await _healthDao.findHealthByDate(
          HealthDataType.step, uid, startTime, endTime);
    } catch (e) {
      print('查询步数数据失败: $e');
      return [];
    }
  }

  /// 查询指定时间范围的睡眠数据
  Future<List<HealthEntity>> getSleepData(
      String uid, int startTime, int endTime) async {
    try {
      return await _healthDao.findHealthByDate(
          HealthDataType.sleep, uid, startTime, endTime);
    } catch (e) {
      print('查询睡眠数据失败: $e');
      return [];
    }
  }

  /// 获取最新的健康数据
  Future<HealthEntity?> getLatestHealthData(int type, String uid) async {
    try {
      return await _healthDao.getHealthLiveDataLast(type, uid);
    } catch (e) {
      print('获取最新健康数据失败: $e');
      return null;
    }
  }

  /// 统计每日健康数据
  Future<List<HealthEntity>> getDailyHealthData(
      String uid, int type, int startTime, int endTime) async {
    try {
      return await _healthDao.countDailyHealthData(
          uid, type, startTime, endTime);
    } catch (e) {
      print('统计每日健康数据失败: $e');
      return [];
    }
  }

  /// 查询未同步的数据
  Future<List<HealthEntity>> getUnsyncedData(String uid) async {
    try {
      return await _healthDao.findBySync(uid, false);
    } catch (e) {
      print('查询未同步数据失败: $e');
      return [];
    }
  }

  /// 标记数据为已同步
  Future<void> markAsSynced(HealthEntity healthEntity) async {
    try {
      final syncedEntity = healthEntity.copyWith(sync: true);
      await _healthDao.insert(syncedEntity);
      print('数据已标记为同步: ${healthEntity.id}');
    } catch (e) {
      print('标记数据同步状态失败: $e');
    }
  }

  /// 删除所有健康数据
  Future<void> deleteAllHealthData() async {
    try {
      await _healthDao.deleteAll();
      print('所有健康数据已删除');
    } catch (e) {
      print('删除健康数据失败: $e');
    }
  }
}
