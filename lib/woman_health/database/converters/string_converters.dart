import 'dart:convert';

/// 字符串转换器
class StringConverters {
  /// 将字符串列表转换为JSON字符串
  static String fromList(List<String> list) {
    return jsonEncode(list);
  }

  /// 将JSON字符串转换为字符串列表
  static List<String> toList(String json) {
    if (json.isEmpty) return [];

    try {
      final List<dynamic> decoded = jsonDecode(json);
      return decoded.map((item) => item.toString()).toList();
    } catch (e) {
      return [];
    }
  }

  /// 将Map转换为JSON字符串
  static String fromMap(Map<String, dynamic> map) {
    return jsonEncode(map);
  }

  /// 将JSON字符串转换为Map
  static Map<String, dynamic> toMap(String json) {
    if (json.isEmpty) return {};

    try {
      final Map<String, dynamic> decoded = jsonDecode(json);
      return decoded;
    } catch (e) {
      return {};
    }
  }

  /// 将对象列表转换为JSON字符串
  static String fromObjectList(List<Map<String, dynamic>> list) {
    return jsonEncode(list);
  }

  /// 将JSON字符串转换为对象列表
  static List<Map<String, dynamic>> toObjectList(String json) {
    if (json.isEmpty) return [];

    try {
      final List<dynamic> decoded = jsonDecode(json);
      return decoded.map((item) => Map<String, dynamic>.from(item)).toList();
    } catch (e) {
      return [];
    }
  }

  /// 将整数列表转换为JSON字符串
  static String fromIntList(List<int> list) {
    return jsonEncode(list);
  }

  /// 将JSON字符串转换为整数列表
  static List<int> toIntList(String json) {
    if (json.isEmpty) return [];

    try {
      final List<dynamic> decoded = jsonDecode(json);
      return decoded.map((item) => item as int).toList();
    } catch (e) {
      return [];
    }
  }

  /// 将双精度列表转换为JSON字符串
  static String fromDoubleList(List<double> list) {
    return jsonEncode(list);
  }

  /// 将JSON字符串转换为双精度列表
  static List<double> toDoubleList(String json) {
    if (json.isEmpty) return [];

    try {
      final List<dynamic> decoded = jsonDecode(json);
      return decoded.map((item) => (item as num).toDouble()).toList();
    } catch (e) {
      return [];
    }
  }

  /// 将布尔值列表转换为JSON字符串
  static String fromBoolList(List<bool> list) {
    return jsonEncode(list);
  }

  /// 将JSON字符串转换为布尔值列表
  static List<bool> toBoolList(String json) {
    if (json.isEmpty) return [];

    try {
      final List<dynamic> decoded = jsonDecode(json);
      return decoded.map((item) => item as bool).toList();
    } catch (e) {
      return [];
    }
  }

  /// 安全的JSON解码
  static dynamic safeJsonDecode(String json) {
    if (json.isEmpty) return null;

    try {
      return jsonDecode(json);
    } catch (e) {
      return null;
    }
  }

  /// 安全的JSON编码
  static String safeJsonEncode(dynamic object) {
    try {
      return jsonEncode(object);
    } catch (e) {
      return '';
    }
  }
}
