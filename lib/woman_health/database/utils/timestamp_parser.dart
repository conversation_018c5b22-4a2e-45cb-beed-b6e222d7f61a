import 'dart:typed_data';

/// 时间戳解析工具类
/// 用于解析Harmony/Android压缩时间戳格式
class TimestampParser {
  /// 基准年份，用于时间戳解码
  static const int baseYear = 2010;

  /// 解析4字节小端时间戳
  ///
  /// [data] 包含时间戳的字节数据
  /// [offset] 时间戳在数据中的偏移量
  /// [debug] 是否打印调试信息，默认为false
  ///
  /// 返回解析后的DateTime对象，如果解析失败则返回null
  static DateTime? parseLittleEndianTimestamp(
    Uint8List data,
    int offset, {
    bool debug = false,
  }) {
    if (data.length < offset + 4) {
      if (debug) print('数据长度不足，无法解析时间戳');
      return null;
    }

    try {
      // 解析4字节小端时间戳
      final rawTimestamp =
          data.buffer.asByteData().getUint32(offset, Endian.little);

      if (debug) {
        print('解析rawTimestamp: $rawTimestamp, offset: $offset');
      }

      // Harmony/Android压缩时间戳解码
      final year = ((rawTimestamp >> 26) & 0x3F) + baseYear;
      final month = (rawTimestamp >> 22) & 0x0F;
      final day = (rawTimestamp >> 17) & 0x1F;
      final hour = (rawTimestamp >> 12) & 0x1F;
      final minute = (rawTimestamp >> 6) & 0x3F;
      final second = rawTimestamp & 0x3F;

      if (debug) {
        print(
            '解码时间戳: year=$year, month=$month, day=$day, hour=$hour, minute=$minute, second=$second');
      }

      // 构造DateTime
      final dateTime = DateTime(year, month, day, hour, minute, second);

      if (debug) {
        print('最终time(millis): ${dateTime.millisecondsSinceEpoch}');
      }

      return dateTime;
    } catch (e) {
      if (debug) {
        print('DateTime构造异常: $e，使用当前时间');
      }
      return DateTime.now();
    }
  }

  /// 解析4字节小端时间戳并返回毫秒时间戳
  ///
  /// [data] 包含时间戳的字节数据
  /// [offset] 时间戳在数据中的偏移量
  /// [debug] 是否打印调试信息，默认为false
  ///
  /// 返回毫秒时间戳，如果解析失败则返回当前时间的毫秒时间戳
  static int parseLittleEndianTimestampToMillis(
    Uint8List data,
    int offset, {
    bool debug = false,
  }) {
    final dateTime = parseLittleEndianTimestamp(data, offset, debug: debug);
    return dateTime?.millisecondsSinceEpoch ??
        DateTime.now().millisecondsSinceEpoch;
  }

  /// 解析时间戳并返回分解的时间组件
  ///
  /// [data] 包含时间戳的字节数据
  /// [offset] 时间戳在数据中的偏移量
  /// [debug] 是否打印调试信息，默认为false
  ///
  /// 返回包含年月日时分秒的Map，如果解析失败则返回null
  static Map<String, int>? parseTimestampComponents(
    Uint8List data,
    int offset, {
    bool debug = false,
  }) {
    if (data.length < offset + 4) {
      if (debug) print('数据长度不足，无法解析时间戳');
      return null;
    }

    try {
      final rawTimestamp =
          data.buffer.asByteData().getUint32(offset, Endian.little);

      if (debug) {
        print('解析rawTimestamp: $rawTimestamp, offset: $offset');
      }

      final year = ((rawTimestamp >> 26) & 0x3F) + baseYear;
      final month = (rawTimestamp >> 22) & 0x0F;
      final day = (rawTimestamp >> 17) & 0x1F;
      final hour = (rawTimestamp >> 12) & 0x1F;
      final minute = (rawTimestamp >> 6) & 0x3F;
      final second = rawTimestamp & 0x3F;

      if (debug) {
        print(
            '解码时间戳: year=$year, month=$month, day=$day, hour=$hour, minute=$minute, second=$second');
      }

      return {
        'year': year,
        'month': month,
        'day': day,
        'hour': hour,
        'minute': minute,
        'second': second,
      };
    } catch (e) {
      if (debug) {
        print('时间戳解析异常: $e');
      }
      return null;
    }
  }
}

/// 使用示例：
/// 
/// ```dart
/// import 'package:your_app/database/utils/timestamp_parser.dart';
/// 
/// // 示例1：解析时间戳为DateTime
/// Uint8List data = Uint8List.fromList([0x12, 0x34, 0x56, 0x78, ...]);
/// DateTime? dateTime = TimestampParser.parseLittleEndianTimestamp(data, 0, debug: true);
/// 
/// // 示例2：解析时间戳为毫秒时间戳
/// int timestamp = TimestampParser.parseLittleEndianTimestampToMillis(data, 0);
/// 
/// // 示例3：解析时间戳为时间组件
/// Map<String, int>? components = TimestampParser.parseTimestampComponents(data, 0);
/// if (components != null) {
///   print('年份: ${components['year']}');
///   print('月份: ${components['month']}');
///   print('日期: ${components['day']}');
/// }
/// ```
