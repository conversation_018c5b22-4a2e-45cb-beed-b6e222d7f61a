import 'dart:io';

import 'package:flutter/foundation.dart';

/// 平台检测工具
class PlatformHelper {
  /// 检测是否为鸿蒙系统
  static bool get isOhos {
    if (kIsWeb) return false;
    if (Platform.isAndroid) {
      // 在Android上检测是否为鸿蒙系统
      // 这里可以根据实际需要添加更精确的检测逻辑
      return false; // 暂时返回false，实际使用时需要根据设备信息判断
    }
    return false;
  }

  /// 检测是否为Android系统
  static bool get isAndroid {
    if (kIsWeb) return false;
    return Platform.isAndroid;
  }

  /// 检测是否为iOS系统
  static bool get isIOS {
    if (kIsWeb) return false;
    return Platform.isIOS;
  }

  /// 检测是否为Web平台
  static bool get isWeb {
    return kIsWeb;
  }

  /// 获取当前平台名称
  static String get platformName {
    if (isWeb) return 'Web';
    if (isAndroid) return 'Android';
    if (isIOS) return 'iOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    return 'Ohos';
  }

  /// 获取数据库包名
  static String get databasePackage {
    if (isOhos) {
      return 'sqflite_ohos';
    } else {
      return 'sqflite';
    }
  }
}
