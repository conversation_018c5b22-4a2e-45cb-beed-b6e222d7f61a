/// 数据库常量定义
class DatabaseConstants {
  // 数据库名称
  static const String healthDatabaseName = 'gs_fit.db';

  // 数据库版本
  static const int healthDatabaseVersion = 10;
  static const int translateDatabaseVersion = 3;
}

/// 健康数据类型常量
class HealthDataType {
  static const int heartRate = 0x01; // 心率数据
  static const int gasPressure = 0xf1; // 气压数据
  static const int altitude = 0xf2; // 海拔数据
  static const int step = 9; // 步数数据
  static const int pressure = 0xf4; // 压力数据
  static const int bloodOxygen = 0x06; // 血氧数据
  static const int trainingLoad = 0xf6; // 训练负荷
  static const int vo2max = 0xf7; // 最大摄氧量
  static const int exerciseRecoveryTime = 0xF8; // 运动恢复时间
  static const int sportInfo = 0xf9; // 运动信息
  static const int sleep = 0x05; // 睡眠数据
  static const int weight = 0xFF; // 体重数据
}

/// 运动类型常量
class SportType {
  static const int none = 0x00;
  static const int outdoor = 0x01;
  static const int indoor = 0x02;

  // 具体运动类型
  static const int outdoorRun = 0x01; // 户外跑步
  static const int indoorRun = 0x02; // 室内跑步
  static const int outdoorWalk = 0x03; // 户外步行
  static const int indoorWalk = 0x04; // 室内步行
  static const int outdoorRide = 0x05; // 户外骑行
  static const int indoorBike = 0x06; // 室内单车
  static const int poolSwimming = 0x07; // 泳池游泳
  static const int mountaineering = 0x08; // 登山
  static const int onFoot = 0x09; // 徒步
  static const int crossCountryRunning = 0x0A; // 越野跑
  static const int freeTraining = 0x0B; // 自由训练

  // 心率模式
  static const int heartRateModeMax = 0x00;
  static const int heartRateModeSave = 0x01;
}

/// 表名常量
class TableNames {
  static const String user = 'User';
  static const String healthEntity = 'HealthEntity';
  static const String sportRecord = 'SportRecord';
  static const String locationEntity = 'LocationEntity';
  static const String chatMessageBean = 'ChatMessageBean';
  static const String praisePearlBean = 'PraisePearlBean';
  static const String smokingBean = 'SmokingBean';
  static const String menstrualRecord = 'MenstrualRecord';
  static const String reminderRecord = 'ReminderRecord';
  static const String reminderRecordTB = 'ReminderRecordTb';
  static const String downloadMusicBean = 'DownloadMusicBean';
  static const String siRecorderBean = 'SIRecorderBean';
  static const String meetingRecorder = 'MeetingRecorder';
  static const String realTimeTranslateRecorder = 'RealTimeTranslateRecorder';
}
