import 'dart:async';

import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

import '../dao/health_dao.dart';
import '../dao/sport_record_dao.dart';
import '../dao/menstrual_record_dao.dart';
import '../utils/database_constants.dart';

/// 健康数据库帮助类
class HealthDatabaseHelper {
  static const String _tag = 'HealthDatabaseHelper';

  static HealthDatabaseHelper? _instance;
  static Database? _database;
  static bool _isInitializing = false;

  // 私有构造函数
  HealthDatabaseHelper._();

  /// 获取单例实例
  static HealthDatabaseHelper get instance {
    _instance ??= HealthDatabaseHelper._();
    return _instance!;
  }

  /// 获取数据库实例
  Future<Database> get database async {
    if (_database != null) return _database!;

    // 防止重复初始化
    if (_isInitializing) {
      // 等待初始化完成
      while (_isInitializing) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return _database!;
    }

    _isInitializing = true;
    try {
      _database = await _initDatabase();
      return _database!;
    } finally {
      _isInitializing = false;
    }
  }

  /// 初始化数据库
  Future<Database> _initDatabase() async {
    try {
      print('$_tag: 开始初始化数据库');

      // 等待Flutter引擎完全初始化
      await Future.delayed(const Duration(milliseconds: 500));

      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, DatabaseConstants.healthDatabaseName);

      print('$_tag: 数据库路径: $path');

      return await openDatabase(
        path,
        version: DatabaseConstants.healthDatabaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        singleInstance: true, // 确保单实例
      );
    } catch (e) {
      print('$_tag: 数据库初始化失败: $e');

      // 如果是鸿蒙系统特有的错误，提供更详细的错误信息
      if (e.toString().contains('databaseFactory not initialized')) {
        throw Exception('数据库工厂未初始化。在鸿蒙系统上，请确保：\n'
            '1. 应用已正确配置鸿蒙权限\n'
            '2. 数据库插件已正确安装\n'
            '3. 应用启动时Flutter引擎已完全初始化');
      }

      rethrow;
    }
  }

  /// 创建数据库表
  Future<void> _onCreate(Database db, int version) async {
    try {
      print('$_tag: 开始创建数据库表，版本: $version');

      // 创建用户表
      await db.execute('''
        CREATE TABLE ${TableNames.user} (
          id TEXT PRIMARY KEY,
          username TEXT NOT NULL,
          email TEXT,
          phone TEXT,
          avatar TEXT,
          gender INTEGER NOT NULL,
          birthday INTEGER,
          height INTEGER,
          weight REAL,
          createTime INTEGER NOT NULL,
          updateTime INTEGER NOT NULL
        )
      ''');

      // 创建健康数据表
      await db.execute('''
        CREATE TABLE ${TableNames.healthEntity} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          uid TEXT NOT NULL,
          type INTEGER NOT NULL,
          version INTEGER NOT NULL,
          time INTEGER NOT NULL,
          crcCode BLOB,
          space INTEGER NOT NULL,
          sync INTEGER NOT NULL,
          data BLOB NOT NULL
        )
      ''');

      // 创建运动记录表
      await db.execute('''
        CREATE TABLE ${TableNames.sportRecord} (
          uid TEXT NOT NULL,
          startTime INTEGER NOT NULL,
          type INTEGER NOT NULL,
          internal INTEGER NOT NULL,
          version INTEGER NOT NULL,
          reserve BLOB,
          duration INTEGER NOT NULL,
          stopTime INTEGER NOT NULL,
          distance INTEGER NOT NULL,
          kcal INTEGER NOT NULL,
          step INTEGER NOT NULL,
          recoveryTime INTEGER NOT NULL,
          sync INTEGER NOT NULL,
          data BLOB,
          PRIMARY KEY (uid, startTime)
        )
      ''');

      // 创建位置信息表
      await db.execute('''
        CREATE TABLE ${TableNames.locationEntity} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          uid TEXT NOT NULL,
          latitude REAL NOT NULL,
          longitude REAL NOT NULL,
          altitude REAL,
          accuracy REAL,
          time INTEGER NOT NULL
        )
      ''');

      // 创建聊天消息表
      await db.execute('''
        CREATE TABLE ${TableNames.chatMessageBean} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          uid TEXT NOT NULL,
          message TEXT NOT NULL,
          type INTEGER NOT NULL,
          time INTEGER NOT NULL,
          typeid INTEGER NOT NULL DEFAULT 0,
          classify INTEGER NOT NULL DEFAULT 0
        )
      ''');

      // 创建点赞珍珠表
      await db.execute('''
        CREATE TABLE ${TableNames.praisePearlBean} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          clickCount INTEGER NOT NULL,
          createDate INTEGER NOT NULL
        )
      ''');

      // 创建吸烟记录表
      await db.execute('''
        CREATE TABLE ${TableNames.smokingBean} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          uid TEXT NOT NULL,
          type INTEGER NOT NULL,
          version INTEGER NOT NULL,
          time INTEGER NOT NULL,
          crcCode BLOB,
          space INTEGER NOT NULL,
          sync INTEGER NOT NULL,
          data BLOB NOT NULL
        )
      ''');

      // 创建月经记录表
      await db.execute('''
        CREATE TABLE ${TableNames.menstrualRecord} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          uid TEXT,
          time INTEGER NOT NULL,
          day INTEGER NOT NULL,
          month INTEGER NOT NULL,
          year INTEGER NOT NULL,
          symptom INTEGER NOT NULL,
          data BLOB,
          isDropletBleeding INTEGER NOT NULL,
          isMenstruation INTEGER NOT NULL,
          bleedingVolume INTEGER NOT NULL
        )
      ''');

      // 创建提醒记录表
      await db.execute('''
        CREATE TABLE ${TableNames.reminderRecord} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          recordId INTEGER NOT NULL,
          uid TEXT,
          titleName TEXT,
          startTime INTEGER NOT NULL,
          endTime INTEGER NOT NULL,
          isAllDay INTEGER NOT NULL,
          remindType INTEGER NOT NULL,
          describe TEXT
        )
      ''');
      await db.execute('''
        CREATE TABLE ${TableNames.reminderRecordTB} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          recordId INTEGER NOT NULL,
          uid TEXT,
          titleName TEXT,
          startTime INTEGER NOT NULL,
          endTime INTEGER NOT NULL,
          isAllDay INTEGER NOT NULL,
          remindType INTEGER NOT NULL,
          reminderTimeOption TEXT,
          describe TEXT
        )
      ''');

      // 创建下载音乐表
      await db.execute('''
        CREATE TABLE ${TableNames.downloadMusicBean} (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          contentId TEXT,
          pic TEXT,
          name TEXT,
          contentName TEXT,
          songBizType INTEGER NOT NULL DEFAULT 0,
          progress INTEGER NOT NULL DEFAULT 0
        )
      ''');

      print('$_tag: 数据库表创建完成');
    } catch (e) {
      print('$_tag: 创建数据库表失败: $e');
      rethrow;
    }
  }

  /// 数据库升级
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    try {
      print('$_tag: 数据库升级: $oldVersion -> $newVersion');

      // 根据版本号执行相应的迁移
      if (oldVersion < 4) {
        // Migration 3→4: 创建点赞珍珠表
        await db.execute('''
          CREATE TABLE IF NOT EXISTS ${TableNames.praisePearlBean} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            clickCount INTEGER NOT NULL,
            createDate INTEGER NOT NULL
          )
        ''');
      }

      if (oldVersion < 5) {
        // Migration 4→5: 为聊天消息表添加字段
        await db.execute('ALTER TABLE ${TableNames.chatMessageBean} ADD COLUMN typeid INTEGER NOT NULL DEFAULT 0');
        await db.execute('ALTER TABLE ${TableNames.chatMessageBean} ADD COLUMN classify INTEGER NOT NULL DEFAULT 0');
      }

      if (oldVersion < 6) {
        // Migration 5→6: 创建吸烟记录表
        await db.execute('''
          CREATE TABLE IF NOT EXISTS ${TableNames.smokingBean} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            uid TEXT NOT NULL,
            type INTEGER NOT NULL,
            version INTEGER NOT NULL,
            time INTEGER NOT NULL,
            crcCode BLOB,
            space INTEGER NOT NULL,
            sync INTEGER NOT NULL,
            data BLOB NOT NULL
          )
        ''');
      }

      if (oldVersion < 7) {
        // Migration 6→7: 创建月经记录表
        await db.execute('''
          CREATE TABLE IF NOT EXISTS ${TableNames.menstrualRecord} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            uid TEXT,
            time INTEGER NOT NULL,
            day INTEGER NOT NULL,
            month INTEGER NOT NULL,
            year INTEGER NOT NULL,
            symptom INTEGER NOT NULL,
            data BLOB,
            isDropletBleeding INTEGER NOT NULL,
            isMenstruation INTEGER NOT NULL,
            bleedingVolume INTEGER NOT NULL
          )
        ''');
      }

      if (oldVersion < 8) {
        // Migration 7→8: 创建提醒记录表
        await db.execute('''
          CREATE TABLE IF NOT EXISTS ${TableNames.reminderRecord} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            recordId INTEGER NOT NULL,
            uid TEXT,
            titleName TEXT,
            startTime INTEGER NOT NULL,
            endTime INTEGER NOT NULL,
            isAllDay INTEGER NOT NULL,
            remindType INTEGER NOT NULL,
            describe TEXT
          )
        ''');
      }

      if (oldVersion < 9) {
        // Migration 8→9: 创建下载音乐表
        await db.execute('''
          CREATE TABLE IF NOT EXISTS ${TableNames.downloadMusicBean} (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            contentId TEXT,
            pic TEXT,
            name TEXT,
            contentName TEXT,
            songBizType INTEGER NOT NULL DEFAULT 0,
            progress INTEGER NOT NULL DEFAULT 0
          )
        ''');
      }

      print('$_tag: 数据库升级完成');
    } catch (e) {
      print('$_tag: 数据库升级失败: $e');
      rethrow;
    }
  }

  /// 获取健康数据DAO
  HealthDao get healthDao => HealthDao(database);

  /// 获取运动记录DAO
  SportRecordDao get sportRecordDao => SportRecordDao(database);

  ///提醒事项
  // ReminderRecordHelper get reminder => ReminderRecordHelper(database);

  ///女性健康
  // WomanHealthHelper get womanHealth => WomanHealthHelper(database);

  /// 获取月经记录DAO
  MenstrualRecordDao get menstrualRecordDao => MenstrualRecordDao(database);

  /// 关闭数据库
  Future<void> close() async {
    try {
      if (_database != null) {
        await _database!.close();
        _database = null;
        print('$_tag: 数据库已关闭');
      }
    } catch (e) {
      print('$_tag: 关闭数据库失败: $e');
    }
  }

  /// 删除数据库
  Future<void> deleteDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, DatabaseConstants.healthDatabaseName);
      await databaseFactory.deleteDatabase(path);
      _database = null;
      print('$_tag: 数据库已删除');
    } catch (e) {
      print('$_tag: 删除数据库失败: $e');
    }
  }
}
