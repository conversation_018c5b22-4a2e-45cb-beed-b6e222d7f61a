import 'dart:typed_data';

import 'package:json_annotation/json_annotation.dart';

import '../utils/database_constants.dart';

part 'health_entity.g.dart';

/// 健康数据实体模型
@JsonSerializable()
class HealthEntity {
  /// 唯一ID
  @Json<PERSON>ey(name: 'id')
  final int? id;

  /// 用户唯一ID
  @Json<PERSON>ey(name: 'uid')
  final String uid;

  /// 数据类型
  @Json<PERSON>ey(name: 'type')
  final int type;

  /// 版本
  @JsonKey(name: 'version')
  final int version;

  /// 开始时间
  @JsonKey(name: 'time')
  final int time;

  /// CRC校验码
  @Json<PERSON>ey(
    name: 'crcCode',
    fromJson: _crcCodeFromJson,
    toJson: _crcCodeToJson,
  )
  final Uint8List? crcCode;

  /// 间隔
  @JsonKey(name: 'space')
  final int space;

  /// 是否同步到服务器
  @<PERSON><PERSON><PERSON><PERSON>(name: 'sync')
  final bool sync;

  /// 原始数据
  @J<PERSON><PERSON><PERSON>(
    name: 'data',
    fromJson: _dataFromJson,
    toJson: _dataToJson,
  )
  final Uint8List data;

  /// 数据状态（非数据库字段）
  @JsonKey(includeFromJson: false, includeToJson: false)
  final int? dataState;

  HealthEntity({
    this.id,
    required this.uid,
    required this.type,
    required this.version,
    required this.time,
    this.crcCode,
    required this.space,
    required this.sync,
    required this.data,
    this.dataState,
  });

  /// 从JSON创建实例
  factory HealthEntity.fromJson(Map<String, dynamic> json) =>
      _$HealthEntityFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$HealthEntityToJson(this);

  /// Uint8List fromJson 转换器
  static Uint8List? _crcCodeFromJson(dynamic json) {
    if (json == null) return null;
    if (json is List) {
      return Uint8List.fromList(json.cast<int>());
    }
    return null;
  }

  /// Uint8List toJson 转换器
  static List<int>? _crcCodeToJson(Uint8List? crcCode) {
    return crcCode?.toList();
  }

  /// Uint8List fromJson 转换器
  static Uint8List _dataFromJson(dynamic json) {
    if (json is List) {
      return Uint8List.fromList(json.cast<int>());
    }
    return Uint8List(0);
  }

  /// Uint8List toJson 转换器
  static List<int> _dataToJson(Uint8List data) {
    return data.toList();
  }

  /// 从原始字节数据创建HealthEntity（对齐Android小文件格式，压缩时间戳解码）
  static HealthEntity? fromBytes(Uint8List dayData) {
    if (dayData.length < 12) return null;

    int offset = 0;
    print('原始数据: ' + dayData.toString());
    offset += 1; // 跳过长度
    print('跳过长度字节，offset: ' + offset.toString());
    final type = dayData[offset];
    print('解析type: ' + type.toString() + ', offset: ' + offset.toString());
    offset += 1;

    // 解析4字节小端时间戳
    final rawTimestamp =
        dayData.buffer.asByteData().getUint32(offset, Endian.little);
    print('解析rawTimestamp: ' +
        rawTimestamp.toString() +
        ', offset: ' +
        offset.toString());
    offset += 4;

    // Harmony/Android压缩时间戳解码
    final year = ((rawTimestamp >> 26) & 0x3F) + 2010;
    final month = (rawTimestamp >> 22) & 0x0F;
    final day = (rawTimestamp >> 17) & 0x1F;
    final hour = (rawTimestamp >> 12) & 0x1F;
    final minute = (rawTimestamp >> 6) & 0x3F;
    final second = rawTimestamp & 0x3F;
    print('解码时间戳: year=' +
        year.toString() +
        ', month=' +
        month.toString() +
        ', day=' +
        day.toString() +
        ', hour=' +
        hour.toString() +
        ', minute=' +
        minute.toString() +
        ', second=' +
        second.toString());

    // 构造DateTime
    DateTime? dateTime;
    try {
      dateTime = DateTime(year, month, day, hour, minute, second);
    } catch (_) {
      print('DateTime构造异常，使用当前时间');
      dateTime = DateTime.now();
    }
    final time = dateTime.millisecondsSinceEpoch;
    print('最终time(millis): ' + time.toString());

    // CRC（2字节，大端序）
    final crcCode = Uint8List.fromList(dayData.sublist(offset, offset + 2));
    print(
        '解析crcCode: ' + crcCode.toString() + ', offset: ' + offset.toString());
    offset += 2;

    // 版本
    final version = dayData[offset];
    print(
        '解析version: ' + version.toString() + ', offset: ' + offset.toString());
    offset += 1;

    // 间隔
    final space = dayData[offset];
    print('解析space: ' + space.toString() + ', offset: ' + offset.toString());
    offset += 1;

    // 保留
    final reserved = Uint8List.fromList(dayData.sublist(offset, offset + 2));
    print('解析reserved: ' +
        reserved.toString() +
        ', offset: ' +
        offset.toString());
    offset += 2;

    // 唯一ID
    final id = time * 1000 + type;
    print('生成id: ' + id.toString());

    return HealthEntity(
      id: id,
      type: type,
      version: version,
      time: time,
      crcCode: crcCode,
      space: space,
      sync: false,
      data: dayData,
      uid: '',
      // 可扩展reserved、payload等字段
    );
  }

  /// 创建副本
  HealthEntity copyWith({
    int? id,
    String? uid,
    int? type,
    int? version,
    int? time,
    Uint8List? crcCode,
    int? space,
    bool? sync,
    Uint8List? data,
    int? dataState,
  }) {
    return HealthEntity(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      type: type ?? this.type,
      version: version ?? this.version,
      time: time ?? this.time,
      crcCode: crcCode ?? this.crcCode,
      space: space ?? this.space,
      sync: sync ?? this.sync,
      data: data ?? this.data,
      dataState: dataState ?? this.dataState,
    );
  }

  /// 获取数据类型名称
  String get typeName {
    switch (type) {
      case HealthDataType.heartRate:
        return '心率';
      case HealthDataType.step:
        return '步数';
      case HealthDataType.sleep:
        return '睡眠';
      case HealthDataType.bloodOxygen:
        return '血氧';
      case HealthDataType.pressure:
        return '压力';
      case HealthDataType.weight:
        return '体重';
      default:
        return '未知';
    }
  }

  @override
  String toString() {
    return 'HealthEntity{id: $id, type: $type, time: $time, space: $space, version: $version, uid: $uid, sync: $sync, data: $data}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is HealthEntity &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          uid == other.uid &&
          type == other.type;

  @override
  int get hashCode => id.hashCode ^ uid.hashCode ^ type.hashCode;

  /// 工具方法：字节转整数（小端序）
  static int _byte2IntLR(int byte) {
    return byte & 0xFF;
  }

  /// 工具方法：小端字节转时间戳
  static int _littleByteToTimeStamp(Uint8List bytes, int offset) {
    return bytes[offset] |
        (bytes[offset + 1] << 8) |
        (bytes[offset + 2] << 16) |
        (bytes[offset + 3] << 24);
  }
}
