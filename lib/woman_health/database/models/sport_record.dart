import 'dart:typed_data';
import 'package:json_annotation/json_annotation.dart';
import '../utils/database_constants.dart';

part 'sport_record.g.dart';

/// 运动记录模型
@JsonSerializable()
class SportRecord {
  /// 用户ID
  @J<PERSON><PERSON><PERSON>(name: 'uid')
  final String uid;

  /// 运动模式
  @Json<PERSON><PERSON>(name: 'type')
  final int type;

  /// 开始时间戳
  @JsonKey(name: 'startTime')
  final int startTime;

  /// 间隔
  @Json<PERSON>ey(name: 'internal')
  final int internal;

  /// 版本
  @JsonKey(name: 'version')
  final int version;

  /// 保留位
  @J<PERSON><PERSON><PERSON>(
    name: 'reserve',
    fromJson: _reserveFromJson,
    toJson: _reserveToJson,
  )
  final Uint8List? reserve;

  /// 时长(秒)
  @JsonKey(name: 'duration')
  final int duration;

  /// 结束时间戳
  @JsonKey(name: 'stopTime')
  final int stopTime;

  /// 距离(米)
  @Json<PERSON>ey(name: 'distance')
  final int distance;

  /// 热量(千卡)
  @Json<PERSON><PERSON>(name: 'kcal')
  final int kcal;

  /// 步数
  @Json<PERSON><PERSON>(name: 'step')
  final int step;

  /// 恢复时间
  @JsonKey(name: 'recoveryTime')
  final int recoveryTime;

  /// 是否同步
  @JsonKey(name: 'sync')
  final bool sync;

  /// 原始数据
  @JsonKey(
    name: 'data',
    fromJson: _dataFromJson,
    toJson: _dataToJson,
  )
  final Uint8List? data;

  SportRecord({
    required this.uid,
    required this.type,
    required this.startTime,
    required this.internal,
    required this.version,
    this.reserve,
    required this.duration,
    required this.stopTime,
    required this.distance,
    required this.kcal,
    required this.step,
    required this.recoveryTime,
    required this.sync,
    this.data,
  });

  /// 从JSON创建实例
  factory SportRecord.fromJson(Map<String, dynamic> json) =>
      _$SportRecordFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$SportRecordToJson(this);

  /// Uint8List fromJson 转换器
  static Uint8List? _reserveFromJson(dynamic json) {
    if (json == null) return null;
    if (json is List) {
      return Uint8List.fromList(json.cast<int>());
    }
    return null;
  }

  /// Uint8List toJson 转换器
  static List<int>? _reserveToJson(Uint8List? reserve) {
    return reserve?.toList();
  }

  /// Uint8List fromJson 转换器
  static Uint8List? _dataFromJson(dynamic json) {
    if (json == null) return null;
    if (json is List) {
      return Uint8List.fromList(json.cast<int>());
    }
    return null;
  }

  /// Uint8List toJson 转换器
  static List<int>? _dataToJson(Uint8List? data) {
    return data?.toList();
  }

  /// 创建副本
  SportRecord copyWith({
    String? uid,
    int? type,
    int? startTime,
    int? internal,
    int? version,
    Uint8List? reserve,
    int? duration,
    int? stopTime,
    int? distance,
    int? kcal,
    int? step,
    int? recoveryTime,
    bool? sync,
    Uint8List? data,
  }) {
    return SportRecord(
      uid: uid ?? this.uid,
      type: type ?? this.type,
      startTime: startTime ?? this.startTime,
      internal: internal ?? this.internal,
      version: version ?? this.version,
      reserve: reserve ?? this.reserve,
      duration: duration ?? this.duration,
      stopTime: stopTime ?? this.stopTime,
      distance: distance ?? this.distance,
      kcal: kcal ?? this.kcal,
      step: step ?? this.step,
      recoveryTime: recoveryTime ?? this.recoveryTime,
      sync: sync ?? this.sync,
      data: data ?? this.data,
    );
  }

  /// 获取运动类型名称
  String get typeName {
    switch (type) {
      case SportType.outdoorRun:
        return '户外跑步';
      case SportType.indoorRun:
        return '室内跑步';
      case SportType.outdoorWalk:
        return '户外步行';
      case SportType.indoorWalk:
        return '室内步行';
      case SportType.outdoorRide:
        return '户外骑行';
      case SportType.indoorBike:
        return '室内单车';
      case SportType.poolSwimming:
        return '泳池游泳';
      case SportType.mountaineering:
        return '登山';
      case SportType.onFoot:
        return '徒步';
      case SportType.crossCountryRunning:
        return '越野跑';
      case SportType.freeTraining:
        return '自由训练';
      default:
        return '未知运动';
    }
  }

  /// 获取运动时长（格式化）
  String get durationText {
    final hours = duration ~/ 3600;
    final minutes = (duration % 3600) ~/ 60;
    final seconds = duration % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// 获取距离（格式化）
  String get distanceText {
    if (distance >= 1000) {
      return '${(distance / 1000).toStringAsFixed(2)}km';
    } else {
      return '${distance}m';
    }
  }

  @override
  String toString() {
    return 'SportRecord{uid: $uid, type: $type, startTime: $startTime, duration: $duration, distance: $distance, kcal: $kcal, step: $step}';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SportRecord &&
          runtimeType == other.runtimeType &&
          uid == other.uid &&
          startTime == other.startTime;

  @override
  int get hashCode => uid.hashCode ^ startTime.hashCode;
}

/// 运动数据信息
class SportInfo {
  final double speed; // 速度
  final int oxygen; // 血氧
  final int heart; // 心率
  final int time; // 时间
  final int stepFreq; // 步频
  final int pace; // 配速

  SportInfo({
    required this.speed,
    required this.oxygen,
    required this.heart,
    required this.time,
    required this.stepFreq,
    required this.pace,
  });

  @override
  String toString() {
    return 'SportInfo{speed: $speed, oxygen: $oxygen, heart: $heart, time: $time, stepFreq: $stepFreq, pace: $pace}';
  }
}

/// 配速信息
class Pace {
  final int n; // 序号
  final int value; // 配速值

  Pace({required this.n, required this.value});

  @override
  String toString() {
    return 'Pace{n: $n, value: $value}';
  }
}
