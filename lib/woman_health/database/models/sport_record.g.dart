// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sport_record.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SportRecord _$SportRecordFromJson(Map<String, dynamic> json) => SportRecord(
      uid: json['uid'] as String,
      type: (json['type'] as num).toInt(),
      startTime: (json['startTime'] as num).toInt(),
      internal: (json['internal'] as num).toInt(),
      version: (json['version'] as num).toInt(),
      reserve: SportRecord._reserveFromJson(json['reserve']),
      duration: (json['duration'] as num).toInt(),
      stopTime: (json['stopTime'] as num).toInt(),
      distance: (json['distance'] as num).toInt(),
      kcal: (json['kcal'] as num).toInt(),
      step: (json['step'] as num).toInt(),
      recoveryTime: (json['recoveryTime'] as num).toInt(),
      sync: json['sync'] as bool,
      data: SportRecord._dataFromJson(json['data']),
    );

Map<String, dynamic> _$SportRecordToJson(SportRecord instance) =>
    <String, dynamic>{
      'uid': instance.uid,
      'type': instance.type,
      'startTime': instance.startTime,
      'internal': instance.internal,
      'version': instance.version,
      'reserve': SportRecord._reserveToJson(instance.reserve),
      'duration': instance.duration,
      'stopTime': instance.stopTime,
      'distance': instance.distance,
      'kcal': instance.kcal,
      'step': instance.step,
      'recoveryTime': instance.recoveryTime,
      'sync': instance.sync,
      'data': SportRecord._dataToJson(instance.data),
    };
