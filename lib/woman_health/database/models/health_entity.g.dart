// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'health_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HealthEntity _$HealthEntityFromJson(Map<String, dynamic> json) => HealthEntity(
      id: (json['id'] as num?)?.toInt(),
      uid: json['uid'] as String,
      type: (json['type'] as num).toInt(),
      version: (json['version'] as num).toInt(),
      time: (json['time'] as num).toInt(),
      crcCode: HealthEntity._crcCodeFromJson(json['crcCode']),
      space: (json['space'] as num).toInt(),
      sync: json['sync'] as bool,
      data: HealthEntity._dataFromJson(json['data']),
    );

Map<String, dynamic> _$HealthEntityToJson(HealthEntity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'uid': instance.uid,
      'type': instance.type,
      'version': instance.version,
      'time': instance.time,
      'crcCode': HealthEntity._crcCodeToJson(instance.crcCode),
      'space': instance.space,
      'sync': instance.sync,
      'data': HealthEntity._dataToJson(instance.data),
    };
