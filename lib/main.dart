import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ohos_app/woman_health/woman_health.dart';

void main() async {
  // 确保Flutter绑定初始化
  WidgetsFlutterBinding.ensureInitialized();

  // 设置系统UI样式
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  // 初始化数据库
  await _initializeDatabase();

  // 运行应用
  runApp(const MyApp());
}

/// 初始化数据库
Future<void> _initializeDatabase() async {
  try {
    await GsFitDatabase.initialize();
    print('✅ GsFit数据库初始化成功');
  } catch (e) {
    print('❌ GsFit数据库初始化失败，但应用继续运行: $e');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
      designSize: const Size(375, 812),
      minTextAdapt: true,
      splitScreenMode: true,
      builder: (context, child) {
        return GetMaterialApp(
          title: 'OHOS健康应用',
          debugShowCheckedModeBanner: false,

          // 使用女性健康模块的主题
          theme: _buildAppTheme(),

          // 设置初始路由
          initialRoute: AppRoutes.initial,

          // 使用女性健康模块的路由配置
          getPages: AppPages.routes,

          // 默认过渡动画
          defaultTransition: Transition.cupertino,
          transitionDuration: const Duration(milliseconds: 300),

          // 路由未找到时的回调
          unknownRoute: GetPage(
            name: '/notfound',
            page: () => const NotFoundPage(),
          ),

          // 全局绑定
          initialBinding: AppBinding(),
        );
      },
    );
  }

  /// 构建应用主题
  ThemeData _buildAppTheme() {
    return ThemeData(
      primarySwatch: Colors.pink,
      primaryColor: const Color(0xFFFF6B9D),
      scaffoldBackgroundColor: const Color(0xFFF8F9FA),
      fontFamily: 'PingFang SC',

      // AppBar主题
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.white,
        elevation: 0,
        iconTheme: IconThemeData(color: Color(0xFF333333)),
        titleTextStyle: TextStyle(
          color: Color(0xFF333333),
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
        systemOverlayStyle: SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
        ),
      ),

      // 卡片主题
      cardTheme: CardTheme(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        color: Colors.white,
      ),

      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFFFF6B9D),
          foregroundColor: Colors.white,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),

      // 颜色方案
      colorScheme: const ColorScheme.light(
        primary: Color(0xFFFF6B9D),
        secondary: Color(0xFF4CAF50),
        surface: Colors.white,
        error: Color(0xFFF44336),
        onPrimary: Colors.white,
        onSecondary: Colors.white,
        onSurface: Color(0xFF333333),
        onError: Colors.white,
      ),

      // 使用Material 3
      useMaterial3: true,
    );
  }
}

/// 应用首页 - 功能选择页面
class MyHomePage extends StatelessWidget {
  const MyHomePage({super.key, required this.title});

  final String title;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(title),
        centerTitle: true,
      ),
      body: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 应用标题
            Text(
              'OHOS健康应用',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              '选择您需要的功能模块',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 48),

            // 功能模块卡片
            _buildFeatureCard(
              context,
              title: '女性健康',
              subtitle: '月经周期管理、预测分析、症状记录',
              icon: Icons.favorite,
              color: const Color(0xFFFF6B9D),
              onTap: () => Get.toNamed(AppRoutes.healthMain),
            ),

            const SizedBox(height: 20),

            _buildFeatureCard(
              context,
              title: '健康引导',
              subtitle: '首次使用设置向导',
              icon: Icons.assistant,
              color: const Color(0xFF4CAF50),
              onTap: () => Get.toNamed(AppRoutes.guide),
            ),

            const SizedBox(height: 20),

            _buildFeatureCard(
              context,
              title: '设置',
              subtitle: '个人设置、通知配置、数据管理',
              icon: Icons.settings,
              color: const Color(0xFF2196F3),
              onTap: () => Get.toNamed(AppRoutes.settings),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建功能卡片
  Widget _buildFeatureCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(20),
          child: Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 32,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF333333),
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: Colors.grey[400],
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 404页面
class NotFoundPage extends StatelessWidget {
  const NotFoundPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('页面未找到'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              '404',
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '页面未找到',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 32),
            ElevatedButton(
              onPressed: () => Get.offAllNamed(AppRoutes.initial),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }
}

/// 全局绑定
class AppBinding extends Bindings {
  @override
  void dependencies() {
    // 注册全局控制器
    Get.lazyPut<HealthMainController>(() => HealthMainController());
    Get.lazyPut<GuideController>(() => GuideController());
  }
}
