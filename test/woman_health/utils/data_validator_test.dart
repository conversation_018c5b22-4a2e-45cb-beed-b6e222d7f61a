import 'package:flutter_test/flutter_test.dart';
import 'package:ohos_app/woman_health/utils/data_validator.dart';
import 'package:ohos_app/woman_health/models/menstrual_record.dart';
import 'package:ohos_app/woman_health/models/hemorrhage_data.dart';

void main() {
  group('DataValidator Tests', () {
    group('MenstrualRecord Validation', () {
      test('should validate valid menstrual record', () {
        final record = MenstrualRecord(
          uid: 'test_user',
          time: DateTime(2024, 1, 15).millisecondsSinceEpoch,
          day: 15,
          month: 1,
          year: 2024,
          isMenstruation: true,
          bleedingVolume: MenstrualRecord.bleedingVolumeSmall,
        );

        final result = DataValidator.validateMenstrualRecord(record);
        expect(result.isValid, true);
        expect(result.errors, isEmpty);
      });

      test('should reject record with empty uid', () {
        final record = MenstrualRecord(
          uid: '',
          time: DateTime(2024, 1, 15).millisecondsSinceEpoch,
          day: 15,
          month: 1,
          year: 2024,
        );

        final result = DataValidator.validateMenstrualRecord(record);
        expect(result.isValid, false);
        expect(result.errors, contains('用户ID不能为空'));
      });

      test('should reject record with invalid date', () {
        final record = MenstrualRecord(
          uid: 'test_user',
          time: DateTime(2024, 1, 15).millisecondsSinceEpoch,
          day: 32, // 无效日期
          month: 1,
          year: 2024,
        );

        final result = DataValidator.validateMenstrualRecord(record);
        expect(result.isValid, false);
        expect(result.errors.any((error) => error.contains('日期无效')), true);
      });

      test('should warn about inconsistent menstruation and bleeding', () {
        final record = MenstrualRecord(
          uid: 'test_user',
          time: DateTime(2024, 1, 15).millisecondsSinceEpoch,
          day: 15,
          month: 1,
          year: 2024,
          isMenstruation: true,
          bleedingVolume: MenstrualRecord.bleedingVolumeAbsence, // 矛盾
        );

        final result = DataValidator.validateMenstrualRecord(record);
        expect(result.warnings, contains('标记为月经但出血量为零'));
      });
    });

    group('HemorrhageData Validation', () {
      test('should validate valid hemorrhage data', () {
        final data = HemorrhageData(
          volume: HemorrhageData.volumeLight,
          color: '鲜红色',
          texture: '液体状',
          duration: 6,
        );

        final result = DataValidator.validateHemorrhageData(data);
        expect(result.isValid, true);
        expect(result.errors, isEmpty);
      });

      test('should reject negative duration', () {
        final data = HemorrhageData(
          volume: HemorrhageData.volumeLight,
          duration: -1,
        );

        final result = DataValidator.validateHemorrhageData(data);
        expect(result.isValid, false);
        expect(result.errors, contains('持续时间不能为负数'));
      });

      test('should warn about long duration', () {
        final data = HemorrhageData(
          volume: HemorrhageData.volumeLight,
          duration: 25, // 超过24小时
        );

        final result = DataValidator.validateHemorrhageData(data);
        expect(result.warnings, contains('持续时间超过24小时，请确认是否正确'));
      });
    });

    group('Cycle Length Validation', () {
      test('should validate normal cycle length', () {
        final result = DataValidator.validateCycleLength(28);
        expect(result.isValid, true);
        expect(result.errors, isEmpty);
      });

      test('should reject too short cycle', () {
        final result = DataValidator.validateCycleLength(20);
        expect(result.isValid, false);
        expect(result.errors, contains('月经周期长度不能少于21天'));
      });

      test('should reject too long cycle', () {
        final result = DataValidator.validateCycleLength(36);
        expect(result.isValid, false);
        expect(result.errors, contains('月经周期长度不能超过35天'));
      });

      test('should warn about borderline cycle length', () {
        final result = DataValidator.validateCycleLength(23);
        expect(result.isValid, true);
        expect(result.warnings, contains('月经周期长度偏离正常范围(24-32天)'));
      });
    });

    group('Menstruation Duration Validation', () {
      test('should validate normal duration', () {
        final result = DataValidator.validateMenstruationDuration(5);
        expect(result.isValid, true);
        expect(result.errors, isEmpty);
      });

      test('should reject zero duration', () {
        final result = DataValidator.validateMenstruationDuration(0);
        expect(result.isValid, false);
        expect(result.errors, contains('月经持续时间不能少于1天'));
      });

      test('should reject too long duration', () {
        final result = DataValidator.validateMenstruationDuration(11);
        expect(result.isValid, false);
        expect(result.errors, contains('月经持续时间不能超过10天'));
      });

      test('should warn about short duration', () {
        final result = DataValidator.validateMenstruationDuration(2);
        expect(result.isValid, true);
        expect(result.warnings, contains('月经持续时间偏离正常范围(3-7天)'));
      });
    });

    group('Age Validation', () {
      test('should validate normal age', () {
        final result = DataValidator.validateAge(25);
        expect(result.isValid, true);
        expect(result.errors, isEmpty);
      });

      test('should reject too young age', () {
        final result = DataValidator.validateAge(9);
        expect(result.isValid, false);
        expect(result.errors, contains('年龄过小'));
      });

      test('should warn about old age', () {
        final result = DataValidator.validateAge(65);
        expect(result.isValid, true);
        expect(result.warnings, contains('年龄较大，请确认是否仍有月经'));
      });
    });

    group('Record Continuity Validation', () {
      test('should validate normal record list', () {
        final records = [
          MenstrualRecord(
            uid: 'test',
            time: DateTime(2024, 1, 1).millisecondsSinceEpoch,
            day: 1,
            month: 1,
            year: 2024,
            isMenstruation: true,
          ),
          MenstrualRecord(
            uid: 'test',
            time: DateTime(2024, 1, 29).millisecondsSinceEpoch,
            day: 29,
            month: 1,
            year: 2024,
            isMenstruation: true,
          ),
        ];

        final result = DataValidator.validateRecordContinuity(records);
        expect(result.isValid, true);
      });

      test('should detect duplicate dates', () {
        final records = [
          MenstrualRecord(
            uid: 'test',
            time: DateTime(2024, 1, 1).millisecondsSinceEpoch,
            day: 1,
            month: 1,
            year: 2024,
          ),
          MenstrualRecord(
            uid: 'test',
            time: DateTime(2024, 1, 1).millisecondsSinceEpoch,
            day: 1,
            month: 1,
            year: 2024,
          ),
        ];

        final result = DataValidator.validateRecordContinuity(records);
        expect(result.isValid, false);
        expect(result.errors.any((error) => error.contains('存在重复日期记录')), true);
      });

      test('should warn about short cycles', () {
        final records = [
          MenstrualRecord(
            uid: 'test',
            time: DateTime(2024, 1, 1).millisecondsSinceEpoch,
            day: 1,
            month: 1,
            year: 2024,
            isMenstruation: true,
          ),
          MenstrualRecord(
            uid: 'test',
            time: DateTime(2024, 1, 15).millisecondsSinceEpoch, // 只有14天间隔
            day: 15,
            month: 1,
            year: 2024,
            isMenstruation: true,
          ),
        ];

        final result = DataValidator.validateRecordContinuity(records);
        expect(result.warnings.any((warning) => warning.contains('月经周期过短')), true);
      });
    });

    group('Temperature Data Validation', () {
      test('should validate normal temperature data', () {
        final temperatureData = {
          DateTime(2024, 1, 1): 36.5,
          DateTime(2024, 1, 2): 36.7,
          DateTime(2024, 1, 3): 36.4,
        };

        final result = DataValidator.validateTemperatureData(temperatureData);
        expect(result.isValid, true);
        expect(result.errors, isEmpty);
      });

      test('should reject extreme temperatures', () {
        final temperatureData = {
          DateTime(2024, 1, 1): 45.0, // 过高
          DateTime(2024, 1, 2): 30.0, // 过低
        };

        final result = DataValidator.validateTemperatureData(temperatureData);
        expect(result.isValid, false);
        expect(result.errors.length, 2);
      });

      test('should warn about borderline temperatures', () {
        final temperatureData = {
          DateTime(2024, 1, 1): 35.5, // 偏低但在范围内
          DateTime(2024, 1, 2): 38.0, // 偏高但在范围内
        };

        final result = DataValidator.validateTemperatureData(temperatureData);
        expect(result.isValid, true);
        expect(result.warnings.length, 2);
      });
    });
  });
}
