import 'package:flutter_test/flutter_test.dart';
import 'package:ohos_app/woman_health/models/menstrual_record.dart';

void main() {
  group('MenstrualRecord Tests', () {
    test('should create MenstrualRecord with required fields', () {
      final record = MenstrualRecord(
        uid: 'test_user',
        time: DateTime(2024, 1, 15).millisecondsSinceEpoch,
        day: 15,
        month: 1,
        year: 2024,
      );

      expect(record.uid, 'test_user');
      expect(record.day, 15);
      expect(record.month, 1);
      expect(record.year, 2024);
      expect(record.symptom, 0);
      expect(record.isDropletBleeding, false);
      expect(record.isMenstruation, false);
      expect(record.bleedingVolume, MenstrualRecord.bleedingVolumeAbsence);
    });

    test('should set time correctly', () {
      final record = MenstrualRecord(
        uid: 'test_user',
        time: 0,
        day: 1,
        month: 1,
        year: 2024,
      );

      record.setTime(2024, 2, 15);

      expect(record.year, 2024);
      expect(record.month, 2);
      expect(record.day, 15);
      expect(record.dateTime, DateTime(2024, 2, 15));
    });

    test('should handle symptoms correctly', () {
      final record = MenstrualRecord(
        uid: 'test_user',
        time: DateTime.now().millisecondsSinceEpoch,
        day: 1,
        month: 1,
        year: 2024,
      );

      // Test adding symptoms
      record.addSymptom(MenstrualRecord.symptomsTypeHeadache);
      expect(record.hasSymptom(MenstrualRecord.symptomsTypeHeadache), true);
      expect(record.hasSymptom(MenstrualRecord.symptomsTypeFatigue), false);

      // Test adding multiple symptoms
      record.addSymptom(MenstrualRecord.symptomsTypeFatigue);
      expect(record.hasSymptom(MenstrualRecord.symptomsTypeHeadache), true);
      expect(record.hasSymptom(MenstrualRecord.symptomsTypeFatigue), true);

      // Test removing symptoms
      record.removeSymptom(MenstrualRecord.symptomsTypeHeadache);
      expect(record.hasSymptom(MenstrualRecord.symptomsTypeHeadache), false);
      expect(record.hasSymptom(MenstrualRecord.symptomsTypeFatigue), true);
    });

    test('should get symptom names correctly', () {
      expect(MenstrualRecord.getSymptomName(MenstrualRecord.symptomsTypeHeadache), '头痛');
      expect(MenstrualRecord.getSymptomName(MenstrualRecord.symptomsTypeFatigue), '疲劳');
      expect(MenstrualRecord.getSymptomName(0x999), '未知症状');
    });

    test('should get bleeding volume names correctly', () {
      expect(MenstrualRecord.getBleedingVolumeName(MenstrualRecord.bleedingVolumeAbsence), '未出血');
      expect(MenstrualRecord.getBleedingVolumeName(MenstrualRecord.bleedingVolumeSmall), '少量');
      expect(MenstrualRecord.getBleedingVolumeName(MenstrualRecord.bleedingVolumeMedium), '中量');
      expect(MenstrualRecord.getBleedingVolumeName(MenstrualRecord.bleedingVolumeMany), '多量');
    });

    test('should copy with new values', () {
      final original = MenstrualRecord(
        uid: 'test_user',
        time: DateTime(2024, 1, 15).millisecondsSinceEpoch,
        day: 15,
        month: 1,
        year: 2024,
        symptom: MenstrualRecord.symptomsTypeHeadache,
        isMenstruation: true,
        bleedingVolume: MenstrualRecord.bleedingVolumeSmall,
      );

      final copied = original.copyWith(
        day: 16,
        symptom: MenstrualRecord.symptomsTypeFatigue,
        bleedingVolume: MenstrualRecord.bleedingVolumeMedium,
      );

      expect(copied.uid, original.uid);
      expect(copied.month, original.month);
      expect(copied.year, original.year);
      expect(copied.isMenstruation, original.isMenstruation);
      
      expect(copied.day, 16);
      expect(copied.symptom, MenstrualRecord.symptomsTypeFatigue);
      expect(copied.bleedingVolume, MenstrualRecord.bleedingVolumeMedium);
    });

    test('should handle equality correctly', () {
      final record1 = MenstrualRecord(
        id: 1,
        uid: 'test_user',
        time: DateTime(2024, 1, 15).millisecondsSinceEpoch,
        day: 15,
        month: 1,
        year: 2024,
      );

      final record2 = MenstrualRecord(
        id: 1,
        uid: 'test_user',
        time: DateTime(2024, 1, 15).millisecondsSinceEpoch,
        day: 15,
        month: 1,
        year: 2024,
      );

      final record3 = MenstrualRecord(
        id: 2,
        uid: 'test_user',
        time: DateTime(2024, 1, 15).millisecondsSinceEpoch,
        day: 15,
        month: 1,
        year: 2024,
      );

      expect(record1, equals(record2));
      expect(record1, isNot(equals(record3)));
    });
  });
}
