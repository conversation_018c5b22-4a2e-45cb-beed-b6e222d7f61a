import 'package:flutter_test/flutter_test.dart';
import 'package:ohos_app/woman_health/services/ovulation_calculator.dart';
import 'package:ohos_app/woman_health/models/menstrual_record.dart';

void main() {
  group('OvulationCalculator Tests', () {
    test('should calculate basic ovulation correctly', () {
      final lastMenstruation = DateTime(2024, 1, 1);
      const cycleLength = 28;
      
      final result = OvulationCalculator.calculateOvulation(
        lastMenstruationDate: lastMenstruation,
        cycleLength: cycleLength,
      );

      // 排卵日应该是下次月经前14天
      final expectedOvulation = DateTime(2024, 1, 15); // 1月1日 + 28天 - 14天
      expect(result.ovulationDate.day, expectedOvulation.day);
      expect(result.ovulationDate.month, expectedOvulation.month);
      
      // 易孕期应该是排卵日前5天到后1天
      expect(result.fertileStartDate, DateTime(2024, 1, 10));
      expect(result.fertileEndDate, DateTime(2024, 1, 16));
      
      expect(result.isForecast, true);
      expect(result.accuracy, greaterThan(0));
    });

    test('should calculate ovulation with different cycle lengths', () {
      final lastMenstruation = DateTime(2024, 1, 1);
      
      // 测试21天周期
      final result21 = OvulationCalculator.calculateOvulation(
        lastMenstruationDate: lastMenstruation,
        cycleLength: 21,
      );
      expect(result21.ovulationDate, DateTime(2024, 1, 8)); // 1 + 21 - 14 = 8
      
      // 测试35天周期
      final result35 = OvulationCalculator.calculateOvulation(
        lastMenstruationDate: lastMenstruation,
        cycleLength: 35,
      );
      expect(result35.ovulationDate, DateTime(2024, 1, 22)); // 1 + 35 - 14 = 22
    });

    test('should calculate ovulation from history', () {
      final records = [
        MenstrualRecord(
          uid: 'test',
          time: DateTime(2023, 11, 1).millisecondsSinceEpoch,
          day: 1,
          month: 11,
          year: 2023,
          isMenstruation: true,
        ),
        MenstrualRecord(
          uid: 'test',
          time: DateTime(2023, 11, 29).millisecondsSinceEpoch,
          day: 29,
          month: 11,
          year: 2023,
          isMenstruation: true,
        ),
        MenstrualRecord(
          uid: 'test',
          time: DateTime(2023, 12, 27).millisecondsSinceEpoch,
          day: 27,
          month: 12,
          year: 2023,
          isMenstruation: true,
        ),
      ];

      final results = OvulationCalculator.calculateOvulationFromHistory(
        menstrualHistory: records,
        predictMonths: 3,
      );

      expect(results.length, 3);
      expect(results.every((r) => r.isForecast), true);
    });

    test('should handle empty history', () {
      final results = OvulationCalculator.calculateOvulationFromHistory(
        menstrualHistory: [],
        predictMonths: 3,
      );

      expect(results, isEmpty);
    });

    test('should check if date is in fertile period', () {
      final result = OvulationResult(
        ovulationDate: DateTime(2024, 1, 15),
        fertileStartDate: DateTime(2024, 1, 10),
        fertileEndDate: DateTime(2024, 1, 16),
        accuracy: 85,
      );

      expect(result.isInFertilePeriod(DateTime(2024, 1, 9)), false);
      expect(result.isInFertilePeriod(DateTime(2024, 1, 10)), true);
      expect(result.isInFertilePeriod(DateTime(2024, 1, 13)), true);
      expect(result.isInFertilePeriod(DateTime(2024, 1, 16)), true);
      expect(result.isInFertilePeriod(DateTime(2024, 1, 17)), false);
    });

    test('should check if date is ovulation day', () {
      final result = OvulationResult(
        ovulationDate: DateTime(2024, 1, 15),
        fertileStartDate: DateTime(2024, 1, 10),
        fertileEndDate: DateTime(2024, 1, 16),
        accuracy: 85,
      );

      expect(result.isOvulationDay(DateTime(2024, 1, 14)), false);
      expect(result.isOvulationDay(DateTime(2024, 1, 15)), true);
      expect(result.isOvulationDay(DateTime(2024, 1, 16)), false);
    });

    test('should calculate temperature-based ovulation', () {
      final temperatureData = {
        DateTime(2024, 1, 10): 36.2,
        DateTime(2024, 1, 11): 36.3,
        DateTime(2024, 1, 12): 36.1,
        DateTime(2024, 1, 13): 36.2,
        DateTime(2024, 1, 14): 36.4, // 体温开始升高
        DateTime(2024, 1, 15): 36.7,
        DateTime(2024, 1, 16): 36.8,
        DateTime(2024, 1, 17): 36.6,
        DateTime(2024, 1, 18): 36.7,
        DateTime(2024, 1, 19): 36.8,
        DateTime(2024, 1, 20): 36.7,
      };

      final ovulationDate = OvulationCalculator.calculateOvulationFromTemperature(temperatureData);
      
      // 应该检测到体温升高的转折点
      expect(ovulationDate, isNotNull);
    });

    test('should handle insufficient temperature data', () {
      final temperatureData = {
        DateTime(2024, 1, 10): 36.2,
        DateTime(2024, 1, 11): 36.3,
      };

      final ovulationDate = OvulationCalculator.calculateOvulationFromTemperature(temperatureData);
      expect(ovulationDate, isNull);
    });

    test('should calculate mucus-based ovulation', () {
      final mucusData = {
        DateTime(2024, 1, 10): 1, // 干燥
        DateTime(2024, 1, 11): 2, // 粘稠
        DateTime(2024, 1, 12): 3, // 乳白色
        DateTime(2024, 1, 13): 4, // 透明拉丝状
        DateTime(2024, 1, 14): 4, // 透明拉丝状
        DateTime(2024, 1, 15): 3, // 乳白色
        DateTime(2024, 1, 16): 2, // 粘稠
      };

      final ovulationDate = OvulationCalculator.calculateOvulationFromMucus(mucusData);
      
      // 应该返回最后一个透明拉丝状粘液的日期
      expect(ovulationDate, DateTime(2024, 1, 14));
    });

    test('should handle empty mucus data', () {
      final ovulationDate = OvulationCalculator.calculateOvulationFromMucus({});
      expect(ovulationDate, isNull);
    });
  });
}
