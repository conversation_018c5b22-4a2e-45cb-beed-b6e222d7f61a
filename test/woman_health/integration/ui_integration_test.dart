import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:ohos_app/woman_health/woman_health.dart';

void main() {
  group('女性健康UI集成测试', () {
    testWidgets('应用启动和主页面显示测试', (WidgetTester tester) async {
      // 构建应用
      await tester.pumpWidget(const WomanHealthApp());
      
      // 等待应用加载完成
      await tester.pumpAndSettle();
      
      // 验证主页面元素
      expect(find.text('女性健康'), findsOneWidget);
      expect(find.byType(CustomCalendar), findsOneWidget);
      expect(find.text('快捷操作'), findsOneWidget);
    });

    testWidgets('引导页面流程测试', (WidgetTester tester) async {
      // 构建引导页面
      await tester.pumpWidget(
        GetMaterialApp(
          home: const GuideView(),
          getPages: AppPages.routes,
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证欢迎页面
      expect(find.text('欢迎使用女性健康'), findsOneWidget);
      expect(find.text('下一步'), findsOneWidget);
      
      // 点击下一步
      await tester.tap(find.text('下一步'));
      await tester.pumpAndSettle();
      
      // 验证进入设置页面
      expect(find.text('设置月经持续时间'), findsOneWidget);
    });

    testWidgets('日历组件交互测试', (WidgetTester tester) async {
      // 创建测试数据
      final calendarData = <CalendarSelectData>[
        CalendarSelectData(
          year: 2024,
          month: 3,
          day: 15,
          isCurrentDay: true,
        ),
      ];
      
      // 构建日历组件
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: CustomCalendar(
              calendarData: calendarData,
              selectedDate: DateTime(2024, 3, 15),
              onDateSelected: (date) {},
            ),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证日历显示
      expect(find.text('15'), findsOneWidget);
      expect(find.byType(CustomCalendar), findsOneWidget);
    });

    testWidgets('通用组件测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                PrimaryButton(
                  text: '测试按钮',
                  onPressed: () {},
                ),
                const CustomTextField(
                  hintText: '测试输入框',
                ),
                CardContainer(
                  child: const Text('测试卡片'),
                ),
                OptionTile(
                  title: '测试选项',
                  isSelected: true,
                  onTap: () {},
                ),
              ],
            ),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证组件显示
      expect(find.text('测试按钮'), findsOneWidget);
      expect(find.text('测试卡片'), findsOneWidget);
      expect(find.text('测试选项'), findsOneWidget);
      expect(find.byType(TextField), findsOneWidget);
    });

    testWidgets('添加记录页面测试', (WidgetTester tester) async {
      // 初始化控制器
      Get.put(HealthMainController());
      
      await tester.pumpWidget(
        GetMaterialApp(
          home: const AddRecordView(),
          getPages: AppPages.routes,
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证页面元素
      expect(find.text('添加记录'), findsOneWidget);
      expect(find.text('选择日期'), findsOneWidget);
      expect(find.text('月经状态'), findsOneWidget);
      expect(find.text('出血量'), findsOneWidget);
      expect(find.text('症状记录'), findsOneWidget);
    });

    testWidgets('预测页面测试', (WidgetTester tester) async {
      // 初始化控制器
      Get.put(HealthMainController());
      
      await tester.pumpWidget(
        GetMaterialApp(
          home: const ForecastView(),
          getPages: AppPages.routes,
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证页面元素
      expect(find.text('预测分析'), findsOneWidget);
      expect(find.text('下次月经预测'), findsOneWidget);
      expect(find.text('下次排卵预测'), findsOneWidget);
      expect(find.text('预测日历'), findsOneWidget);
    });

    testWidgets('统计页面测试', (WidgetTester tester) async {
      // 初始化控制器
      Get.put(HealthMainController());
      
      await tester.pumpWidget(
        GetMaterialApp(
          home: const StatisticsView(),
          getPages: AppPages.routes,
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证页面元素
      expect(find.text('统计分析'), findsOneWidget);
      expect(find.text('健康概览'), findsOneWidget);
      expect(find.text('周期统计'), findsOneWidget);
      expect(find.text('症状统计'), findsOneWidget);
    });

    testWidgets('设置页面测试', (WidgetTester tester) async {
      // 初始化控制器
      Get.put(HealthMainController());
      
      await tester.pumpWidget(
        GetMaterialApp(
          home: const SettingsView(),
          getPages: AppPages.routes,
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证页面元素
      expect(find.text('设置'), findsOneWidget);
      expect(find.text('个人资料'), findsOneWidget);
      expect(find.text('周期设置'), findsOneWidget);
      expect(find.text('通知设置'), findsOneWidget);
      expect(find.text('数据管理'), findsOneWidget);
    });

    testWidgets('日历详情页面测试', (WidgetTester tester) async {
      // 初始化控制器
      Get.put(HealthMainController());
      
      await tester.pumpWidget(
        GetMaterialApp(
          home: const CalendarView(),
          getPages: AppPages.routes,
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证页面元素
      expect(find.text('健康日历'), findsOneWidget);
      expect(find.text('快捷操作'), findsOneWidget);
      expect(find.text('预测信息'), findsOneWidget);
      expect(find.byType(CustomCalendar), findsOneWidget);
    });

    testWidgets('路由导航测试', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          initialRoute: AppRoutes.healthMain,
          getPages: AppPages.routes,
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证初始路由
      expect(find.text('女性健康'), findsOneWidget);
      
      // 测试路由是否正确配置
      expect(AppPages.routes.length, greaterThan(5));
      expect(AppRoutes.getAllRoutes().length, greaterThan(10));
    });

    testWidgets('主题和样式测试', (WidgetTester tester) async {
      await tester.pumpWidget(const WomanHealthApp());
      await tester.pumpAndSettle();
      
      // 获取主题
      final context = tester.element(find.byType(MaterialApp));
      final theme = Theme.of(context);
      
      // 验证主题配置
      expect(theme.primaryColor, const Color(0xFFFF6B9D));
      expect(theme.scaffoldBackgroundColor, const Color(0xFFF8F9FA));
    });
  });

  group('响应式设计测试', () {
    testWidgets('不同屏幕尺寸适配测试', (WidgetTester tester) async {
      // 测试小屏幕
      await tester.binding.setSurfaceSize(const Size(320, 568));
      await tester.pumpWidget(const WomanHealthApp());
      await tester.pumpAndSettle();
      
      expect(find.text('女性健康'), findsOneWidget);
      
      // 测试大屏幕
      await tester.binding.setSurfaceSize(const Size(414, 896));
      await tester.pumpWidget(const WomanHealthApp());
      await tester.pumpAndSettle();
      
      expect(find.text('女性健康'), findsOneWidget);
      
      // 重置屏幕尺寸
      await tester.binding.setSurfaceSize(null);
    });
  });

  group('性能测试', () {
    testWidgets('页面加载性能测试', (WidgetTester tester) async {
      final stopwatch = Stopwatch()..start();
      
      await tester.pumpWidget(const WomanHealthApp());
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      
      // 验证加载时间在合理范围内（小于1秒）
      expect(stopwatch.elapsedMilliseconds, lessThan(1000));
    });
  });
}
